"""
Weather API client for NFL game conditions
Weather significantly impacts NFL DFS - wind affects passing, rain affects fumbles
"""
import requests
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import asyncio
import aiohttp
from loguru import logger

from config import WEATHER_API_KEY, WEATHER_BASE_URL


class WeatherClient:
    """Client for weather data affecting NFL games"""
    
    def __init__(self, api_key: str = WEATHER_API_KEY):
        self.api_key = api_key
        self.base_url = WEATHER_BASE_URL
        self.session = requests.Session()
        
        # NFL stadium locations (city, state) for weather lookup
        self.stadium_locations = {
            'ARI': ('Glendale', 'AZ'),
            'ATL': ('Atlanta', 'GA'),
            'BAL': ('Baltimore', 'MD'),
            'BUF': ('Orchard Park', 'NY'),
            'CAR': ('Charlotte', 'NC'),
            'CHI': ('Chicago', 'IL'),
            'CIN': ('Cincinnati', 'OH'),
            'CLE': ('Cleveland', 'OH'),
            'DAL': ('Arlington', 'TX'),
            'DEN': ('Denver', 'CO'),
            'DET': ('Detroit', 'MI'),
            'GB': ('Green Bay', 'WI'),
            'HOU': ('Houston', 'TX'),
            'IND': ('Indianapolis', 'IN'),
            'JAX': ('Jacksonville', 'FL'),
            'KC': ('Kansas City', 'MO'),
            'LV': ('Las Vegas', 'NV'),
            'LAC': ('Los Angeles', 'CA'),
            'LAR': ('Los Angeles', 'CA'),
            'MIA': ('Miami Gardens', 'FL'),
            'MIN': ('Minneapolis', 'MN'),
            'NE': ('Foxborough', 'MA'),
            'NO': ('New Orleans', 'LA'),
            'NYG': ('East Rutherford', 'NJ'),
            'NYJ': ('East Rutherford', 'NJ'),
            'PHI': ('Philadelphia', 'PA'),
            'PIT': ('Pittsburgh', 'PA'),
            'SF': ('Santa Clara', 'CA'),
            'SEA': ('Seattle', 'WA'),
            'TB': ('Tampa', 'FL'),
            'TEN': ('Nashville', 'TN'),
            'WAS': ('Landover', 'MD')
        }
        
        # Dome/indoor stadiums (weather doesn't matter)
        self.dome_teams = {
            'ARI', 'ATL', 'DET', 'HOU', 'IND', 'LV', 'LAR', 'MIN', 'NO', 'DAL'
        }
    
    def get_coordinates(self, city: str, state: str) -> Tuple[float, float]:
        """Get latitude/longitude for city"""
        try:
            url = f"http://api.openweathermap.org/geo/1.0/direct"
            params = {
                'q': f"{city},{state},US",
                'limit': 1,
                'appid': self.api_key
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data:
                return data[0]['lat'], data[0]['lon']
            else:
                logger.warning(f"No coordinates found for {city}, {state}")
                return 0.0, 0.0
                
        except Exception as e:
            logger.error(f"Error getting coordinates for {city}, {state}: {e}")
            return 0.0, 0.0
    
    async def get_current_weather(self, team: str) -> Dict:
        """Get current weather for team's stadium"""
        try:
            if team in self.dome_teams:
                return {
                    'team': team,
                    'is_dome': True,
                    'temperature': 72,
                    'humidity': 50,
                    'wind_speed': 0,
                    'wind_direction': 0,
                    'precipitation': 0,
                    'conditions': 'Indoor',
                    'visibility': 10
                }
            
            if team not in self.stadium_locations:
                logger.warning(f"Unknown team: {team}")
                return {}
            
            city, state = self.stadium_locations[team]
            lat, lon = self.get_coordinates(city, state)
            
            if lat == 0 and lon == 0:
                return {}
            
            url = f"{self.base_url}/weather"
            params = {
                'lat': lat,
                'lon': lon,
                'appid': self.api_key,
                'units': 'imperial'
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            return {
                'team': team,
                'is_dome': False,
                'temperature': data['main']['temp'],
                'feels_like': data['main']['feels_like'],
                'humidity': data['main']['humidity'],
                'pressure': data['main']['pressure'],
                'wind_speed': data['wind']['speed'],
                'wind_direction': data['wind'].get('deg', 0),
                'precipitation': data.get('rain', {}).get('1h', 0) + data.get('snow', {}).get('1h', 0),
                'conditions': data['weather'][0]['description'],
                'visibility': data.get('visibility', 10000) / 1000,  # Convert to miles
                'clouds': data['clouds']['all']
            }
            
        except Exception as e:
            logger.error(f"Error getting weather for {team}: {e}")
            return {}
    
    async def get_forecast(self, team: str, game_time: datetime) -> Dict:
        """Get weather forecast for game time"""
        try:
            if team in self.dome_teams:
                return {
                    'team': team,
                    'is_dome': True,
                    'temperature': 72,
                    'humidity': 50,
                    'wind_speed': 0,
                    'wind_direction': 0,
                    'precipitation_probability': 0,
                    'conditions': 'Indoor'
                }
            
            if team not in self.stadium_locations:
                return {}
            
            city, state = self.stadium_locations[team]
            lat, lon = self.get_coordinates(city, state)
            
            if lat == 0 and lon == 0:
                return {}
            
            url = f"{self.base_url}/forecast"
            params = {
                'lat': lat,
                'lon': lon,
                'appid': self.api_key,
                'units': 'imperial'
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            # Find forecast closest to game time
            target_timestamp = game_time.timestamp()
            closest_forecast = None
            min_diff = float('inf')
            
            for forecast in data['list']:
                forecast_time = forecast['dt']
                diff = abs(forecast_time - target_timestamp)
                if diff < min_diff:
                    min_diff = diff
                    closest_forecast = forecast
            
            if not closest_forecast:
                return {}
            
            return {
                'team': team,
                'is_dome': False,
                'game_time': game_time.isoformat(),
                'temperature': closest_forecast['main']['temp'],
                'feels_like': closest_forecast['main']['feels_like'],
                'humidity': closest_forecast['main']['humidity'],
                'wind_speed': closest_forecast['wind']['speed'],
                'wind_direction': closest_forecast['wind'].get('deg', 0),
                'precipitation_probability': closest_forecast.get('pop', 0) * 100,
                'conditions': closest_forecast['weather'][0]['description'],
                'visibility': closest_forecast.get('visibility', 10000) / 1000
            }
            
        except Exception as e:
            logger.error(f"Error getting forecast for {team}: {e}")
            return {}
    
    async def get_game_weather(self, games_df: pd.DataFrame) -> pd.DataFrame:
        """Get weather for all games in schedule"""
        weather_data = []
        
        for _, game in games_df.iterrows():
            home_team = game['home_team']
            game_time = pd.to_datetime(game['date'])
            
            weather = await self.get_forecast(home_team, game_time)
            if weather:
                weather['game_id'] = game['game_id']
                weather_data.append(weather)
            
            # Rate limiting
            await asyncio.sleep(0.1)
        
        return pd.DataFrame(weather_data)
    
    def calculate_weather_impact(self, weather_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate weather impact scores for DFS"""
        df = weather_df.copy()
        
        # Initialize impact scores
        df['passing_impact'] = 0.0
        df['rushing_impact'] = 0.0
        df['kicking_impact'] = 0.0
        df['total_impact'] = 0.0
        
        for idx, row in df.iterrows():
            if row['is_dome']:
                continue
            
            # Wind impact (most important for passing and kicking)
            wind_speed = row['wind_speed']
            if wind_speed > 20:
                df.at[idx, 'passing_impact'] = -0.3  # Severe negative impact
                df.at[idx, 'kicking_impact'] = -0.4
            elif wind_speed > 15:
                df.at[idx, 'passing_impact'] = -0.2
                df.at[idx, 'kicking_impact'] = -0.3
            elif wind_speed > 10:
                df.at[idx, 'passing_impact'] = -0.1
                df.at[idx, 'kicking_impact'] = -0.2
            
            # Precipitation impact
            precip_prob = row.get('precipitation_probability', 0)
            if precip_prob > 70:
                df.at[idx, 'passing_impact'] -= 0.15
                df.at[idx, 'rushing_impact'] += 0.1  # Favor rushing in bad weather
            elif precip_prob > 40:
                df.at[idx, 'passing_impact'] -= 0.1
                df.at[idx, 'rushing_impact'] += 0.05
            
            # Temperature impact (extreme cold)
            temp = row['temperature']
            if temp < 20:
                df.at[idx, 'passing_impact'] -= 0.1
                df.at[idx, 'kicking_impact'] -= 0.2
            elif temp < 32:
                df.at[idx, 'passing_impact'] -= 0.05
                df.at[idx, 'kicking_impact'] -= 0.1
            
            # Calculate total impact
            df.at[idx, 'total_impact'] = (
                df.at[idx, 'passing_impact'] + 
                df.at[idx, 'rushing_impact'] + 
                df.at[idx, 'kicking_impact']
            ) / 3
        
        return df


# Example usage
async def main():
    client = WeatherClient()
    
    # Test current weather
    weather = await client.get_current_weather('GB')  # Green Bay
    print("Green Bay weather:", weather)
    
    # Test dome team
    dome_weather = await client.get_current_weather('NO')  # New Orleans (dome)
    print("New Orleans weather:", dome_weather)


if __name__ == "__main__":
    asyncio.run(main())
