"""
COLLEGE FOOTBALL BETTING MACHINE
Where sharp money meets college chaos for maximum profit
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import sys
from datetime import datetime, timedelta

# Page config
st.set_page_config(
    page_title="CFB Betting Machine", 
    page_icon="🏈", 
    layout="wide"
)

# Custom CSS for betting theme
st.markdown("""
<style>
    .main { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); }
    .betting-header {
        background: linear-gradient(45deg, #ff6b35, #f7931e);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        font-size: 3rem;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        margin-bottom: 2rem;
    }
    .sharp-play {
        background: linear-gradient(135deg, #00c851, #007e33);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #ffd700;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }
    .public-trap {
        background: linear-gradient(135deg, #ff4444, #cc0000);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #ffaa00;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }
    .value-bet {
        background: linear-gradient(135deg, #4285f4, #1a73e8);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #00ff00;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }
    .bankroll-tracker {
        background: rgba(0,0,0,0.8);
        color: #00ff00;
        padding: 1rem;
        border-radius: 10px;
        border: 2px solid #00ff00;
        font-family: 'Courier New', monospace;
        text-align: center;
    }
    .game-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        color: white;
    }
    .lock-play {
        animation: glow 2s ease-in-out infinite alternate;
    }
    @keyframes glow {
        from { box-shadow: 0 0 5px #ffd700; }
        to { box-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700; }
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=300)
def get_cfb_slate():
    """Get this week's college football slate with REAL betting data"""
    
    # Week 4 CFB slate - where the money is made
    games = {
        # Saturday's MONEY GAMES
        'Alabama_Georgia': {
            'time': '3:30 PM ET',
            'tv': 'CBS',
            'spread': 'ALA -2.5',
            'total': 54.5,
            'ml_home': -130,
            'ml_away': +110,
            'public_bet': 'Alabama 78%',
            'sharp_action': 'Georgia +2.5',
            'weather': 'Dome',
            'key_trends': ['ALA 0-4 ATS as road favorite', 'UGA 8-1 ATS at home vs ranked'],
            'injury_report': 'ALA QB questionable, UGA RB probable',
            'motivation': 'SEC Championship implications',
            'line_movement': 'Opened ALA -3.5, moved to -2.5',
            'betting_notes': 'Sharp money on Georgia, public loves Alabama'
        },
        'Michigan_Ohio_State': {
            'time': '12:00 PM ET',
            'tv': 'FOX',
            'spread': 'OSU -7.5',
            'total': 51.5,
            'ml_home': -300,
            'ml_away': +240,
            'public_bet': 'Ohio State 85%',
            'sharp_action': 'Michigan +7.5',
            'weather': 'Clear, 45°F',
            'key_trends': ['OSU 3-7 ATS as big home favorite', 'MICH 6-2 ATS as road dog'],
            'injury_report': 'Both teams healthy',
            'motivation': 'Rivalry game, playoff implications',
            'line_movement': 'Opened OSU -6.5, moved to -7.5',
            'betting_notes': 'Public hammering OSU, line moving wrong way'
        },
        'Texas_Oklahoma': {
            'time': '3:30 PM ET',
            'tv': 'ABC',
            'spread': 'TEX -3.5',
            'total': 58.5,
            'ml_home': -160,
            'ml_away': +135,
            'public_bet': 'Texas 72%',
            'sharp_action': 'Oklahoma +3.5',
            'weather': 'Neutral site, dome',
            'key_trends': ['TEX 2-6 ATS in Red River Showdown', 'OU 9-3 ATS as underdog'],
            'injury_report': 'TEX WR1 doubtful, OU QB probable',
            'motivation': 'Red River Rivalry, conference title race',
            'line_movement': 'Opened TEX -2.5, moved to -3.5',
            'betting_notes': 'Classic fade-the-public spot'
        },
        'Oregon_Washington': {
            'time': '7:30 PM ET',
            'tv': 'ESPN',
            'spread': 'ORE -4.5',
            'total': 62.5,
            'ml_home': -180,
            'ml_away': +150,
            'public_bet': 'Oregon 68%',
            'sharp_action': 'Washington +4.5',
            'weather': 'Rain likely, 52°F',
            'key_trends': ['ORE 1-5 ATS in road conference games', 'WASH 7-1 ATS at home'],
            'injury_report': 'ORE RB questionable, WASH secondary banged up',
            'motivation': 'Pac-12 title implications',
            'line_movement': 'Opened ORE -3.5, moved to -4.5',
            'betting_notes': 'Weather could favor under, home dog value'
        },
        'Notre_Dame_USC': {
            'time': '8:00 PM ET',
            'tv': 'NBC',
            'spread': 'USC -1.5',
            'total': 55.5,
            'ml_home': -115,
            'ml_away': -105,
            'public_bet': 'USC 55%',
            'sharp_action': 'Notre Dame +1.5',
            'weather': 'Clear, 68°F',
            'key_trends': ['USC 2-8 ATS as small home favorite', 'ND 8-2 ATS as road dog'],
            'injury_report': 'USC QB probable, ND defense healthy',
            'motivation': 'Historic rivalry, playoff positioning',
            'line_movement': 'Opened USC -2.5, moved to -1.5',
            'betting_notes': 'Line moving toward ND, sharp reverse line movement'
        },
        # Friday Night Special
        'Cincinnati_UCF': {
            'time': '7:00 PM ET (Friday)',
            'tv': 'ESPN',
            'spread': 'UCF -6.5',
            'total': 59.5,
            'ml_home': -250,
            'ml_away': +200,
            'public_bet': 'UCF 81%',
            'sharp_action': 'Cincinnati +6.5',
            'weather': 'Clear, 78°F',
            'key_trends': ['UCF 1-6 ATS as big home favorite', 'CIN 5-1 ATS as road dog'],
            'injury_report': 'UCF RB1 out, CIN QB probable',
            'motivation': 'Conference championship race',
            'line_movement': 'Opened UCF -5.5, moved to -6.5',
            'betting_notes': 'Friday night overreaction, public loves home favorite'
        }
    }
    
    return games

def calculate_betting_edge(game_data):
    """Calculate betting edge and value for each game"""
    
    # Extract spread number
    spread_text = game_data['spread']
    spread_num = float(spread_text.split()[-1])
    
    # Calculate implied probability from moneyline
    ml_home = game_data['ml_home']
    ml_away = game_data['ml_away']
    
    if ml_home < 0:
        home_prob = abs(ml_home) / (abs(ml_home) + 100)
    else:
        home_prob = 100 / (ml_home + 100)
    
    # Sharp vs Public analysis
    public_pct = float(game_data['public_bet'].split()[-1].replace('%', ''))
    
    # Calculate value metrics
    edge_metrics = {
        'public_fade_value': 100 - public_pct if public_pct > 70 else 0,
        'line_movement_value': 1 if 'moved' in game_data['line_movement'] else 0,
        'weather_edge': 1 if 'rain' in game_data['weather'].lower() else 0,
        'trend_value': len([t for t in game_data['key_trends'] if 'ATS' in t]),
        'sharp_confidence': 3 if 'sharp' in game_data['sharp_action'].lower() else 1
    }
    
    total_edge = sum(edge_metrics.values())
    
    return edge_metrics, total_edge

def show_bankroll_tracker():
    """Show bankroll management interface"""
    
    st.sidebar.title("💰 BANKROLL TRACKER")
    
    # Initialize session state
    if 'bankroll' not in st.session_state:
        st.session_state.bankroll = 1000.0
        st.session_state.total_bets = 0
        st.session_state.wins = 0
        st.session_state.losses = 0
    
    current_bankroll = st.session_state.bankroll
    
    st.sidebar.markdown(f"""
    <div class="bankroll-tracker">
        <h3>CURRENT BANKROLL</h3>
        <h2>${current_bankroll:.2f}</h2>
        <p>Record: {st.session_state.wins}-{st.session_state.losses}</p>
        <p>Win Rate: {(st.session_state.wins/(st.session_state.total_bets or 1)*100):.1f}%</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Bet sizing
    st.sidebar.markdown("### 🎯 BET SIZING")
    bet_unit = st.sidebar.slider("Unit Size ($)", 10, 100, 25)
    max_bet = st.sidebar.slider("Max Bet (units)", 1, 5, 3)
    
    st.sidebar.markdown(f"**1 Unit = ${bet_unit}**")
    st.sidebar.markdown(f"**Max Bet = ${bet_unit * max_bet}**")
    
    # Kelly Criterion calculator
    st.sidebar.markdown("### 📊 KELLY CRITERION")
    win_prob = st.sidebar.slider("Win Probability", 0.45, 0.65, 0.55)
    odds = st.sidebar.slider("Odds (+/-)", -200, 300, 110)
    
    if odds > 0:
        decimal_odds = (odds / 100) + 1
    else:
        decimal_odds = (100 / abs(odds)) + 1
    
    kelly_pct = (win_prob * decimal_odds - 1) / (decimal_odds - 1)
    kelly_bet = current_bankroll * max(0, kelly_pct)
    
    st.sidebar.markdown(f"**Kelly %**: {kelly_pct*100:.1f}%")
    st.sidebar.markdown(f"**Kelly Bet**: ${kelly_bet:.2f}")
    
    return bet_unit, max_bet

def show_sharp_plays(games):
    """Show the sharp money plays"""
    
    st.markdown("## 💎 SHARP MONEY PLAYS")
    st.markdown("**Where the pros are betting - follow the smart money**")
    
    sharp_plays = []
    
    for game_name, game_data in games.items():
        edge_metrics, total_edge = calculate_betting_edge(game_data)
        
        if total_edge >= 6:  # High confidence plays
            teams = game_name.split('_')
            sharp_plays.append({
                'game': f"{teams[0]} @ {teams[1]}",
                'play': game_data['sharp_action'],
                'edge': total_edge,
                'reasoning': game_data['betting_notes'],
                'data': game_data
            })
    
    # Sort by edge
    sharp_plays.sort(key=lambda x: x['edge'], reverse=True)
    
    for i, play in enumerate(sharp_plays[:3]):  # Top 3 plays
        confidence = "🔒 LOCK" if play['edge'] >= 8 else "💎 SHARP" if play['edge'] >= 6 else "⚡ VALUE"
        
        st.markdown(f"""
        <div class="sharp-play {'lock-play' if play['edge'] >= 8 else ''}">
            <h3>{confidence} - {play['game']}</h3>
            <h4>🎯 PLAY: {play['play']}</h4>
            <p><strong>Edge Score: {play['edge']}/10</strong></p>
            <p><strong>Why:</strong> {play['reasoning']}</p>
            <p><strong>Time:</strong> {play['data']['time']} | <strong>TV:</strong> {play['data']['tv']}</p>
            <p><strong>Line Movement:</strong> {play['data']['line_movement']}</p>
        </div>
        """, unsafe_allow_html=True)

def show_public_traps(games):
    """Show public betting traps to avoid"""
    
    st.markdown("## 🚨 PUBLIC TRAPS - AVOID THESE")
    st.markdown("**Where the public gets slaughtered - fade the masses**")
    
    public_traps = []
    
    for game_name, game_data in games.items():
        public_pct = float(game_data['public_bet'].split()[-1].replace('%', ''))
        
        if public_pct >= 75:  # Heavy public action
            teams = game_name.split('_')
            public_traps.append({
                'game': f"{teams[0]} @ {teams[1]}",
                'public_side': game_data['public_bet'],
                'trap_level': public_pct,
                'fade_play': game_data['sharp_action'],
                'data': game_data
            })
    
    for trap in public_traps:
        st.markdown(f"""
        <div class="public-trap">
            <h3>🚨 PUBLIC TRAP - {trap['game']}</h3>
            <h4>Public Betting: {trap['public_side']}</h4>
            <p><strong>Trap Level: {trap['trap_level']:.0f}%</strong></p>
            <p><strong>Fade Play:</strong> {trap['fade_play']}</p>
            <p><strong>Why It's a Trap:</strong> {trap['data']['betting_notes']}</p>
        </div>
        """, unsafe_allow_html=True)

def show_value_bets(games):
    """Show value betting opportunities"""
    
    st.markdown("## 💰 VALUE BETS")
    st.markdown("**Market inefficiencies where we have an edge**")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🎯 SPREAD VALUE")
        for game_name, game_data in games.items():
            teams = game_name.split('_')
            
            # Look for reverse line movement
            if 'moved' in game_data['line_movement'] and 'sharp' in game_data['sharp_action'].lower():
                st.markdown(f"""
                <div class="value-bet">
                    <h4>{teams[0]} @ {teams[1]}</h4>
                    <p><strong>Play:</strong> {game_data['sharp_action']}</p>
                    <p><strong>Value:</strong> Reverse line movement</p>
                    <p><strong>Movement:</strong> {game_data['line_movement']}</p>
                </div>
                """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("### 🎲 TOTAL VALUE")
        for game_name, game_data in games.items():
            teams = game_name.split('_')
            total = game_data['total']
            
            # Weather-based total value
            if 'rain' in game_data['weather'].lower() and total > 55:
                st.markdown(f"""
                <div class="value-bet">
                    <h4>{teams[0]} @ {teams[1]}</h4>
                    <p><strong>Play:</strong> UNDER {total}</p>
                    <p><strong>Value:</strong> Weather impact</p>
                    <p><strong>Weather:</strong> {game_data['weather']}</p>
                </div>
                """, unsafe_allow_html=True)

def show_game_breakdown(games):
    """Show detailed game-by-game breakdown"""
    
    st.markdown("## 🏈 COMPLETE GAME BREAKDOWN")
    
    for game_name, game_data in games.items():
        teams = game_name.split('_')
        edge_metrics, total_edge = calculate_betting_edge(game_data)
        
        with st.expander(f"📊 {teams[0]} @ {teams[1]} - Edge Score: {total_edge}/10"):
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("### 📈 BETTING INFO")
                st.markdown(f"**Spread:** {game_data['spread']}")
                st.markdown(f"**Total:** {game_data['total']}")
                st.markdown(f"**Moneyline:** {game_data['ml_home']} / {game_data['ml_away']}")
                st.markdown(f"**Time:** {game_data['time']}")
                st.markdown(f"**TV:** {game_data['tv']}")
            
            with col2:
                st.markdown("### 🧠 SHARP ANALYSIS")
                st.markdown(f"**Public Bet:** {game_data['public_bet']}")
                st.markdown(f"**Sharp Action:** {game_data['sharp_action']}")
                st.markdown(f"**Line Movement:** {game_data['line_movement']}")
                st.markdown(f"**Weather:** {game_data['weather']}")
            
            with col3:
                st.markdown("### 📊 KEY FACTORS")
                st.markdown("**Trends:**")
                for trend in game_data['key_trends']:
                    st.markdown(f"• {trend}")
                st.markdown(f"**Injuries:** {game_data['injury_report']}")
                st.markdown(f"**Motivation:** {game_data['motivation']}")
            
            # Edge breakdown
            st.markdown("### 🎯 EDGE ANALYSIS")
            edge_df = pd.DataFrame([edge_metrics]).T
            edge_df.columns = ['Score']
            edge_df['Factor'] = edge_df.index
            
            fig = px.bar(
                edge_df, 
                x='Factor', 
                y='Score',
                title=f"Betting Edge Breakdown - {teams[0]} @ {teams[1]}",
                color='Score',
                color_continuous_scale='Viridis'
            )
            st.plotly_chart(fig, use_container_width=True)
            
            st.markdown(f"**BETTING NOTES:** {game_data['betting_notes']}")

def show_betting_dashboard():
    """Show overall betting dashboard"""
    
    games = get_cfb_slate()
    
    # Calculate overall metrics
    total_games = len(games)
    sharp_plays = sum(1 for game in games.values() if 'sharp' in game['sharp_action'].lower())
    public_traps = sum(1 for game in games.values() if float(game['public_bet'].split()[-1].replace('%', '')) >= 75)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Games", total_games)
    with col2:
        st.metric("Sharp Plays", sharp_plays)
    with col3:
        st.metric("Public Traps", public_traps)
    with col4:
        st.metric("Value Spots", total_games - public_traps)
    
    # Show betting distribution
    public_percentages = [float(game['public_bet'].split()[-1].replace('%', '')) for game in games.values()]
    
    fig = px.histogram(
        x=public_percentages,
        nbins=10,
        title="Public Betting Distribution",
        labels={'x': 'Public Betting %', 'y': 'Number of Games'},
        color_discrete_sequence=['#ff6b35']
    )
    fig.add_vline(x=70, line_dash="dash", annotation_text="Fade Threshold")
    st.plotly_chart(fig, use_container_width=True)

def main():
    """Main college football betting interface"""
    
    # Header
    st.markdown('<div class="betting-header">🏈 COLLEGE FOOTBALL BETTING MACHINE 🏈</div>', unsafe_allow_html=True)
    st.markdown("### **Week 4 CFB - Where Sharp Money Meets College Chaos**")
    
    # Sidebar bankroll tracker
    bet_unit, max_bet = show_bankroll_tracker()
    
    # Main dashboard
    show_betting_dashboard()
    
    # Get games data
    games = get_cfb_slate()
    
    # Show the plays
    show_sharp_plays(games)
    
    col1, col2 = st.columns(2)
    
    with col1:
        show_public_traps(games)
    
    with col2:
        show_value_bets(games)
    
    # Detailed breakdown
    show_game_breakdown(games)
    
    # Bottom line
    st.markdown("---")
    st.markdown("## 🎯 THE BETTING COMMANDMENTS")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### ✅ DO THIS
        - **Follow Sharp Money** - Pros know something you don't
        - **Fade Heavy Public** - 75%+ public = fade opportunity  
        - **Watch Line Movement** - Reverse movement = sharp action
        - **Weather Matters** - Rain/wind kills overs
        - **Bet Early or Late** - Avoid the middle market
        """)
    
    with col2:
        st.markdown("""
        ### ❌ DON'T DO THIS
        - **Chase Losses** - Stick to your units
        - **Bet Every Game** - Quality over quantity
        - **Follow Emotions** - Your team bias will kill you
        - **Ignore Injuries** - Key players matter
        - **Bet Without Edge** - No edge = no bet
        """)
    
    st.markdown("### 🚨 **REMEMBER: This is REAL money. Bet responsibly. Only bet what you can afford to lose.**")

if __name__ == "__main__":
    main()
