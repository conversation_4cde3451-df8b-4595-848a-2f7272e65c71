"""
Sports Odds API client for Vegas lines and betting data
Vegas odds are crucial for DFS - they predict game flow, totals, and player usage
"""
import requests
import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import asyncio
from loguru import logger

from config import ODDS_API_KEY, ODDS_BASE_URL


class OddsClient:
    """Client for sports betting odds data"""
    
    def __init__(self, api_key: str = ODDS_API_KEY):
        self.api_key = api_key
        self.base_url = ODDS_BASE_URL
        self.session = requests.Session()
        
        # Team name mappings for odds API
        self.team_mappings = {
            'ARI': 'Arizona Cardinals',
            'ATL': 'Atlanta Falcons',
            'BAL': 'Baltimore Ravens',
            'BUF': 'Buffalo Bills',
            'CAR': 'Carolina Panthers',
            'CHI': 'Chicago Bears',
            'CIN': 'Cincinnati Bengals',
            'CLE': 'Cleveland Browns',
            'DAL': 'Dallas Cowboys',
            'DEN': 'Denver Broncos',
            'DET': 'Detroit Lions',
            'GB': 'Green Bay Packers',
            'HOU': 'Houston Texans',
            'IND': 'Indianapolis Colts',
            'JAX': 'Jacksonville Jaguars',
            'KC': 'Kansas City Chiefs',
            'LV': 'Las Vegas Raiders',
            'LAC': 'Los Angeles Chargers',
            'LAR': 'Los Angeles Rams',
            'MIA': 'Miami Dolphins',
            'MIN': 'Minnesota Vikings',
            'NE': 'New England Patriots',
            'NO': 'New Orleans Saints',
            'NYG': 'New York Giants',
            'NYJ': 'New York Jets',
            'PHI': 'Philadelphia Eagles',
            'PIT': 'Pittsburgh Steelers',
            'SF': 'San Francisco 49ers',
            'SEA': 'Seattle Seahawks',
            'TB': 'Tampa Bay Buccaneers',
            'TEN': 'Tennessee Titans',
            'WAS': 'Washington Commanders'
        }
    
    async def get_nfl_odds(self, markets: List[str] = None) -> pd.DataFrame:
        """
        Get NFL odds for various markets
        
        Args:
            markets: List of markets to get odds for
                    ['spreads', 'totals', 'h2h', 'player_props']
        """
        if markets is None:
            markets = ['spreads', 'totals', 'h2h']
        
        all_odds = []
        
        for market in markets:
            try:
                url = f"{self.base_url}/sports/americanfootball_nfl/odds"
                params = {
                    'apiKey': self.api_key,
                    'regions': 'us',
                    'markets': market,
                    'oddsFormat': 'american',
                    'dateFormat': 'iso'
                }
                
                response = self.session.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                for game in data:
                    game_info = {
                        'game_id': game['id'],
                        'sport': game['sport_key'],
                        'commence_time': game['commence_time'],
                        'home_team': game['home_team'],
                        'away_team': game['away_team'],
                        'market': market
                    }
                    
                    # Process bookmaker odds
                    for bookmaker in game.get('bookmakers', []):
                        bookmaker_name = bookmaker['key']
                        
                        for market_data in bookmaker.get('markets', []):
                            if market_data['key'] == market:
                                
                                if market == 'h2h':  # Moneyline
                                    for outcome in market_data['outcomes']:
                                        odds_info = game_info.copy()
                                        odds_info.update({
                                            'bookmaker': bookmaker_name,
                                            'team': outcome['name'],
                                            'moneyline': outcome['price']
                                        })
                                        all_odds.append(odds_info)
                                
                                elif market == 'spreads':
                                    for outcome in market_data['outcomes']:
                                        odds_info = game_info.copy()
                                        odds_info.update({
                                            'bookmaker': bookmaker_name,
                                            'team': outcome['name'],
                                            'spread': outcome['point'],
                                            'spread_odds': outcome['price']
                                        })
                                        all_odds.append(odds_info)
                                
                                elif market == 'totals':
                                    for outcome in market_data['outcomes']:
                                        odds_info = game_info.copy()
                                        odds_info.update({
                                            'bookmaker': bookmaker_name,
                                            'total_type': outcome['name'],  # Over/Under
                                            'total_points': outcome['point'],
                                            'total_odds': outcome['price']
                                        })
                                        all_odds.append(odds_info)
                
                # Rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error getting {market} odds: {e}")
                continue
        
        return pd.DataFrame(all_odds)
    
    async def get_player_props(self) -> pd.DataFrame:
        """Get player prop betting odds"""
        try:
            url = f"{self.base_url}/sports/americanfootball_nfl/odds"
            params = {
                'apiKey': self.api_key,
                'regions': 'us',
                'markets': 'player_pass_tds,player_pass_yds,player_rush_yds,player_receptions',
                'oddsFormat': 'american',
                'dateFormat': 'iso'
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            props = []
            for game in data:
                for bookmaker in game.get('bookmakers', []):
                    for market in bookmaker.get('markets', []):
                        for outcome in market.get('outcomes', []):
                            prop_info = {
                                'game_id': game['id'],
                                'commence_time': game['commence_time'],
                                'bookmaker': bookmaker['key'],
                                'market': market['key'],
                                'player_name': outcome.get('description', ''),
                                'prop_type': outcome['name'],  # Over/Under
                                'line': outcome.get('point', 0),
                                'odds': outcome['price']
                            }
                            props.append(prop_info)
            
            return pd.DataFrame(props)
            
        except Exception as e:
            logger.error(f"Error getting player props: {e}")
            return pd.DataFrame()
    
    def calculate_implied_probability(self, american_odds: int) -> float:
        """Convert American odds to implied probability"""
        if american_odds > 0:
            return 100 / (american_odds + 100)
        else:
            return abs(american_odds) / (abs(american_odds) + 100)
    
    def process_odds_for_dfs(self, odds_df: pd.DataFrame) -> pd.DataFrame:
        """Process odds data for DFS insights"""
        if odds_df.empty:
            return pd.DataFrame()
        
        processed = []
        
        # Group by game
        for game_id, game_odds in odds_df.groupby('game_id'):
            game_info = {
                'game_id': game_id,
                'commence_time': game_odds['commence_time'].iloc[0],
                'home_team': game_odds['home_team'].iloc[0],
                'away_team': game_odds['away_team'].iloc[0]
            }
            
            # Process spreads
            spreads = game_odds[game_odds['market'] == 'spreads']
            if not spreads.empty:
                home_spreads = spreads[spreads['team'] == game_info['home_team']]
                away_spreads = spreads[spreads['team'] == game_info['away_team']]
                
                if not home_spreads.empty:
                    game_info['home_spread'] = home_spreads['spread'].mean()
                    game_info['home_spread_odds'] = home_spreads['spread_odds'].mean()
                
                if not away_spreads.empty:
                    game_info['away_spread'] = away_spreads['spread'].mean()
                    game_info['away_spread_odds'] = away_spreads['spread_odds'].mean()
            
            # Process totals
            totals = game_odds[game_odds['market'] == 'totals']
            if not totals.empty:
                over_totals = totals[totals['total_type'] == 'Over']
                if not over_totals.empty:
                    game_info['total_points'] = over_totals['total_points'].mean()
                    game_info['over_odds'] = over_totals['total_odds'].mean()
                
                under_totals = totals[totals['total_type'] == 'Under']
                if not under_totals.empty:
                    game_info['under_odds'] = under_totals['total_odds'].mean()
            
            # Process moneylines
            moneylines = game_odds[game_odds['market'] == 'h2h']
            if not moneylines.empty:
                home_ml = moneylines[moneylines['team'] == game_info['home_team']]
                away_ml = moneylines[moneylines['team'] == game_info['away_team']]
                
                if not home_ml.empty:
                    game_info['home_moneyline'] = home_ml['moneyline'].mean()
                    game_info['home_win_prob'] = self.calculate_implied_probability(
                        game_info['home_moneyline']
                    )
                
                if not away_ml.empty:
                    game_info['away_moneyline'] = away_ml['moneyline'].mean()
                    game_info['away_win_prob'] = self.calculate_implied_probability(
                        game_info['away_moneyline']
                    )
            
            # Calculate game pace and scoring environment
            if 'total_points' in game_info:
                total = game_info['total_points']
                if total > 50:
                    game_info['pace_environment'] = 'High'
                elif total > 45:
                    game_info['pace_environment'] = 'Medium'
                else:
                    game_info['pace_environment'] = 'Low'
                
                game_info['projected_home_score'] = total / 2
                game_info['projected_away_score'] = total / 2
                
                # Adjust for spread
                if 'home_spread' in game_info:
                    spread = game_info['home_spread']
                    game_info['projected_home_score'] += spread / 2
                    game_info['projected_away_score'] -= spread / 2
            
            processed.append(game_info)
        
        return pd.DataFrame(processed)


# Example usage
async def main():
    client = OddsClient()
    
    # Get NFL odds
    odds = await client.get_nfl_odds(['spreads', 'totals', 'h2h'])
    print(f"Found {len(odds)} odds entries")
    
    if not odds.empty:
        # Process for DFS
        processed = client.process_odds_for_dfs(odds)
        print(f"Processed {len(processed)} games")
        print(processed.head())


if __name__ == "__main__":
    asyncio.run(main())
