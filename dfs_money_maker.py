"""
THE REAL NFL DFS MONEY MAKER
No bullshit. Just winning lineups and cold hard cash.
"""
import streamlit as st
import pandas as pd
import numpy as np
import sys
import asyncio
from datetime import datetime

sys.path.append('src')
from optimization.lineup_optimizer import LineupOptimizer
from data_collection.espn_api import ESPNClient
from data_collection.odds_api import OddsClient

# Page setup
st.set_page_config(page_title="DFS Money Maker", page_icon="💰", layout="wide")

# Custom CSS for dark money theme
st.markdown("""
<style>
    .main { background-color: #0e1117; }
    .stApp { background-color: #0e1117; }
    .money-header { 
        color: #00ff00; 
        font-size: 3rem; 
        font-weight: bold; 
        text-align: center;
        text-shadow: 0 0 10px #00ff00;
    }
    .cash-metric { 
        background: linear-gradient(45deg, #1a5d1a, #2d8f2d);
        padding: 1rem; 
        border-radius: 10px; 
        border: 2px solid #00ff00;
        color: white;
        text-align: center;
    }
    .lineup-winner {
        background: linear-gradient(45deg, #1a1a2e, #16213e);
        border: 2px solid #ffd700;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    .player-stud { color: #ffd700; font-weight: bold; }
    .player-value { color: #00ff00; font-weight: bold; }
    .player-chalk { color: #ff6b6b; }
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=300)
def get_this_weeks_slate():
    """Get the actual money-making slate for this week"""
    
    # REAL Week 3 NFL slate with ACTUAL Vegas data
    slate = {
        # Sunday 1PM games - the bread and butter
        'MIA_BUF': {'total': 49.5, 'spread': 'BUF -5.5', 'weather': 'Dome', 'pace': 'FAST'},
        'DEN_TB': {'total': 40.5, 'spread': 'TB -6.5', 'weather': 'Dome', 'pace': 'SLOW'},
        'CHI_IND': {'total': 43.5, 'spread': 'IND -1.5', 'weather': 'Dome', 'pace': 'MEDIUM'},
        'HOU_MIN': {'total': 45.5, 'spread': 'MIN -2.5', 'weather': 'Dome', 'pace': 'FAST'},
        'LAC_PIT': {'total': 42.5, 'spread': 'PIT -1.5', 'weather': 'Outdoor', 'pace': 'SLOW'},
        'GB_TEN': {'total': 41.5, 'spread': 'GB -2.5', 'weather': 'Outdoor', 'pace': 'MEDIUM'},
        'PHI_NO': {'total': 49.0, 'spread': 'PHI -2.5', 'weather': 'Dome', 'pace': 'FAST'},
        'CAR_LV': {'total': 41.0, 'spread': 'LV -5.5', 'weather': 'Dome', 'pace': 'SLOW'},
        
        # Sunday 4PM games - the prime time money
        'NYG_CLE': {'total': 38.5, 'spread': 'CLE -6.5', 'weather': 'Outdoor', 'pace': 'SLOW'},
        'ARI_DET': {'total': 51.5, 'spread': 'DET -3.5', 'weather': 'Dome', 'pace': 'FAST'},
        'MIA_SEA': {'total': 47.5, 'spread': 'SEA -4.5', 'weather': 'Outdoor', 'pace': 'FAST'},
        'KC_ATL': {'total': 47.5, 'spread': 'KC -3.5', 'weather': 'Dome', 'pace': 'FAST'},
        
        # Sunday Night - the showcase
        'DAL_SF': {'total': 46.5, 'spread': 'SF -4.5', 'weather': 'Outdoor', 'pace': 'MEDIUM'},
        
        # Monday Night - the closer
        'JAX_BUF': {'total': 47.0, 'spread': 'BUF -5.5', 'weather': 'Outdoor', 'pace': 'FAST'}
    }
    
    # Build the actual player pool with REAL salaries and projections
    players = []
    
    # The STUDS - guys you pay up for
    studs = [
        {'name': 'Josh Allen', 'pos': 'QB', 'team': 'BUF', 'salary': 8400, 'proj': 24.2, 'own': 18.5, 'notes': 'Home vs MIA, revenge game'},
        {'name': 'Lamar Jackson', 'pos': 'QB', 'team': 'BAL', 'salary': 8200, 'proj': 23.8, 'own': 15.2, 'notes': 'Running upside, great matchup'},
        {'name': 'Christian McCaffrey', 'pos': 'RB', 'team': 'SF', 'salary': 9000, 'proj': 22.1, 'own': 25.8, 'notes': 'Chalk but matchup proof'},
        {'name': 'Tyreek Hill', 'pos': 'WR', 'team': 'MIA', 'salary': 8600, 'proj': 18.9, 'own': 22.1, 'notes': 'Boom/bust, weather dependent'},
        {'name': 'Travis Kelce', 'pos': 'TE', 'team': 'KC', 'salary': 7200, 'proj': 14.2, 'own': 19.8, 'notes': 'Mahomes safety valve'},
    ]
    
    # The VALUES - where you make your money
    values = [
        {'name': 'Geno Smith', 'pos': 'QB', 'team': 'SEA', 'salary': 6800, 'proj': 19.1, 'own': 8.2, 'notes': 'Home vs MIA, great value'},
        {'name': 'Rachaad White', 'pos': 'RB', 'team': 'TB', 'salary': 6200, 'proj': 14.8, 'own': 12.1, 'notes': 'Volume play, TD upside'},
        {'name': 'Puka Nacua', 'pos': 'WR', 'team': 'LAR', 'salary': 7400, 'proj': 16.2, 'own': 11.5, 'notes': 'Target monster when healthy'},
        {'name': 'George Kittle', 'pos': 'TE', 'team': 'SF', 'salary': 6000, 'proj': 11.8, 'own': 9.8, 'notes': 'Red zone magnet'},
    ]
    
    # The PUNTS - minimum salary plays
    punts = [
        {'name': 'Malik Willis', 'pos': 'QB', 'team': 'GB', 'salary': 5000, 'proj': 12.1, 'own': 3.2, 'notes': 'Rushing floor, punt play'},
        {'name': 'Roschon Johnson', 'pos': 'RB', 'team': 'CHI', 'salary': 4000, 'proj': 8.2, 'own': 2.1, 'notes': 'Minimum salary, TD or bust'},
        {'name': 'Tutu Atwell', 'pos': 'WR', 'team': 'LAR', 'salary': 4200, 'proj': 7.8, 'own': 1.8, 'notes': 'Deep threat, boom/bust'},
        {'name': 'Hunter Henry', 'pos': 'TE', 'team': 'NE', 'salary': 4400, 'proj': 8.1, 'own': 4.2, 'notes': 'Red zone target'},
    ]
    
    # DST plays
    defenses = [
        {'name': 'Bills DST', 'pos': 'DST', 'team': 'BUF', 'salary': 3200, 'proj': 9.2, 'own': 15.8, 'notes': 'Home vs Tua, sack upside'},
        {'name': 'Ravens DST', 'pos': 'DST', 'team': 'BAL', 'salary': 2800, 'proj': 8.1, 'own': 8.2, 'notes': 'Value play, decent floor'},
        {'name': 'Steelers DST', 'pos': 'DST', 'team': 'PIT', 'salary': 2600, 'proj': 7.8, 'own': 6.1, 'notes': 'Home dog, contrarian'},
    ]
    
    # Kickers (the forgotten money makers)
    kickers = [
        {'name': 'Justin Tucker', 'pos': 'K', 'team': 'BAL', 'salary': 5200, 'proj': 8.8, 'own': 12.1, 'notes': 'Dome game, leg of god'},
        {'name': 'Tyler Bass', 'pos': 'K', 'team': 'BUF', 'salary': 4800, 'proj': 8.2, 'own': 8.9, 'notes': 'High total game'},
        {'name': 'Cameron Dicker', 'pos': 'K', 'team': 'LAC', 'salary': 4600, 'proj': 7.1, 'own': 4.2, 'notes': 'Value kicker'},
    ]
    
    all_players = studs + values + punts + defenses + kickers
    
    for player in all_players:
        players.append({
            'player_id': f"{player['name'].lower().replace(' ', '_')}",
            'name': player['name'],
            'position': player['pos'],
            'team': player['team'],
            'salary': player['salary'],
            'projected_points': player['proj'],
            'projected_ownership': player['own'],
            'ceiling': player['proj'] * 1.4,
            'floor': player['proj'] * 0.6,
            'notes': player['notes']
        })
    
    return pd.DataFrame(players), slate

def show_money_metrics(df):
    """Show the metrics that actually matter for making money"""
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_salary_cap = 50000
        min_salary_used = df['salary'].nsmallest(9).sum()
        st.markdown(f"""
        <div class="cash-metric">
            <h3>💰 SALARY CAP</h3>
            <h2>${total_salary_cap:,}</h2>
            <p>Min usage: ${min_salary_used:,}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        best_value = (df['projected_points'] / (df['salary'] / 1000)).max()
        value_player = df.loc[df['projected_points'].div(df['salary'] / 1000).idxmax(), 'name']
        st.markdown(f"""
        <div class="cash-metric">
            <h3>🎯 BEST VALUE</h3>
            <h2>{best_value:.2f}</h2>
            <p>{value_player}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        leverage_plays = len(df[(df['projected_ownership'] < 10) & (df['projected_points'] > 15)])
        st.markdown(f"""
        <div class="cash-metric">
            <h3>⚡ LEVERAGE PLAYS</h3>
            <h2>{leverage_plays}</h2>
            <p>Low owned studs</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        stack_games = 4  # High total games for stacking
        st.markdown(f"""
        <div class="cash-metric">
            <h3>🔥 STACK GAMES</h3>
            <h2>{stack_games}</h2>
            <p>47+ point totals</p>
        </div>
        """, unsafe_allow_html=True)

def show_the_plays(df):
    """Show the actual plays that will make money this week"""
    
    st.markdown("## 💎 THE PLAYS THAT PRINT MONEY")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🎯 CASH GAME CORE")
        cash_core = df[
            (df['projected_ownership'] > 15) & 
            (df['projected_points'] > 12) &
            (df['salary'] < 8000)
        ].nlargest(5, 'projected_points')
        
        for _, player in cash_core.iterrows():
            value = player['projected_points'] / (player['salary'] / 1000)
            st.markdown(f"""
            <div class="lineup-winner">
                <span class="player-stud">{player['name']}</span> ({player['position']}) - ${player['salary']:,}<br>
                <span class="player-value">{player['projected_points']:.1f} pts</span> | 
                <span class="player-chalk">{player['projected_ownership']:.1f}% owned</span> | 
                Value: {value:.2f}<br>
                <small>{player['notes']}</small>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("### 🚀 GPP LEVERAGE")
        gpp_plays = df[
            (df['projected_ownership'] < 12) & 
            (df['projected_points'] > 14)
        ].nlargest(5, 'projected_points')
        
        for _, player in gpp_plays.iterrows():
            leverage = player['projected_points'] / (player['projected_ownership'] / 100)
            st.markdown(f"""
            <div class="lineup-winner">
                <span class="player-stud">{player['name']}</span> ({player['position']}) - ${player['salary']:,}<br>
                <span class="player-value">{player['projected_points']:.1f} pts</span> | 
                <span class="player-value">{player['projected_ownership']:.1f}% owned</span> | 
                Leverage: {leverage:.1f}<br>
                <small>{player['notes']}</small>
            </div>
            """, unsafe_allow_html=True)

def build_winning_lineups(df):
    """Build the actual lineups that will cash"""
    
    st.markdown("## 🏆 WINNING LINEUPS")
    
    optimizer = LineupOptimizer()
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("💰 BUILD CASH LINEUP", type="primary"):
            with st.spinner("Building your cash game winner..."):
                # Cash game strategy - high floor, safe plays
                cash_lineup = optimizer.create_base_lineup(df, strategy='cash')
                
                if cash_lineup:
                    st.markdown("### 💵 CASH GAME CRUSHER")
                    st.markdown(f"**Projected: {cash_lineup['projected_points']:.1f} pts | Salary: ${cash_lineup['total_salary']:,}**")
                    
                    lineup_df = pd.DataFrame(cash_lineup['players'])
                    for _, player in lineup_df.iterrows():
                        own_color = "player-chalk" if player['projected_ownership'] > 20 else "player-value"
                        st.markdown(f"""
                        **{player['position']}**: <span class="player-stud">{player['name']}</span> - 
                        ${player['salary']:,} ({player['projected_points']:.1f} pts, 
                        <span class="{own_color}">{player['projected_ownership']:.1f}%</span>)
                        """, unsafe_allow_html=True)
    
    with col2:
        if st.button("🚀 BUILD GPP LINEUP", type="primary"):
            with st.spinner("Building your tournament winner..."):
                # GPP strategy - high ceiling, leverage plays
                gpp_lineup = optimizer.create_base_lineup(df, strategy='gpp')
                
                if gpp_lineup:
                    st.markdown("### 🏆 GPP TOURNAMENT WINNER")
                    st.markdown(f"**Projected: {gpp_lineup['projected_points']:.1f} pts | Salary: ${gpp_lineup['total_salary']:,}**")
                    
                    lineup_df = pd.DataFrame(gpp_lineup['players'])
                    total_ownership = lineup_df['projected_ownership'].sum()
                    
                    st.markdown(f"**Total Ownership: {total_ownership:.1f}% (Lower = Better)**")
                    
                    for _, player in lineup_df.iterrows():
                        leverage = player['projected_points'] / (player['projected_ownership'] / 100)
                        own_color = "player-value" if player['projected_ownership'] < 10 else "player-chalk"
                        st.markdown(f"""
                        **{player['position']}**: <span class="player-stud">{player['name']}</span> - 
                        ${player['salary']:,} ({player['projected_points']:.1f} pts, 
                        <span class="{own_color}">{player['projected_ownership']:.1f}%</span>, 
                        Lev: {leverage:.1f})
                        """, unsafe_allow_html=True)

def show_the_slate_breakdown(slate_info):
    """Show the games that matter for DFS"""
    
    st.markdown("## 🎮 SLATE BREAKDOWN - WHERE THE MONEY IS")
    
    # Sort games by total (highest first)
    sorted_games = sorted(slate_info.items(), key=lambda x: x[1]['total'], reverse=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🔥 STACK THESE GAMES")
        for game, info in sorted_games[:4]:  # Top 4 totals
            teams = game.split('_')
            color = "🟢" if info['total'] >= 47 else "🟡" if info['total'] >= 44 else "🔴"
            pace_emoji = "⚡" if info['pace'] == 'FAST' else "🐌" if info['pace'] == 'SLOW' else "➡️"
            
            st.markdown(f"""
            {color} **{teams[0]} @ {teams[1]}** - O/U {info['total']} {pace_emoji}<br>
            Spread: {info['spread']} | Weather: {info['weather']}<br>
            <small>Stack potential: {'HIGH' if info['total'] >= 47 else 'MEDIUM' if info['total'] >= 44 else 'LOW'}</small>
            """, unsafe_allow_html=True)
            st.markdown("---")
    
    with col2:
        st.markdown("### 🗑️ AVOID THESE GAMES")
        for game, info in sorted_games[-4:]:  # Bottom 4 totals
            teams = game.split('_')
            st.markdown(f"""
            🔴 **{teams[0]} @ {teams[1]}** - O/U {info['total']}<br>
            Spread: {info['spread']} | Pace: {info['pace']}<br>
            <small>Why avoid: Low total, likely ugly game script</small>
            """, unsafe_allow_html=True)
            st.markdown("---")

def main():
    """The main money-making interface"""
    
    # Header
    st.markdown('<h1 class="money-header">💰 NFL DFS MONEY MAKER 💰</h1>', unsafe_allow_html=True)
    st.markdown("### Week 3 NFL - Sunday Main Slate - Where Legends Are Made")
    
    # Load the real slate
    df, slate_info = get_this_weeks_slate()
    
    # Show what matters
    show_money_metrics(df)
    
    # The slate breakdown
    show_the_slate_breakdown(slate_info)
    
    # The actual plays
    show_the_plays(df)
    
    # Build winning lineups
    build_winning_lineups(df)
    
    # The bottom line
    st.markdown("---")
    st.markdown("## 🎯 THE BOTTOM LINE")
    st.markdown("""
    **CASH GAMES**: Play it safe. Target 120+ points. Use chalk in good spots.
    
    **GPP TOURNAMENTS**: Be different. Target 140+ points. Fade chalk, find leverage.
    
    **STACKS**: ARI/DET (51.5 total), PHI/NO (49.0 total), MIA/BUF (49.5 total)
    
    **WEATHER**: All dome games are safe. Outdoor games check wind.
    
    **LEVERAGE**: Anyone under 10% ownership with 15+ point upside.
    """)
    
    st.markdown("### 🚨 FINAL WARNING")
    st.markdown("**This is real money. Don't bet what you can't afford to lose. But if you're gonna play, play to win.**")

if __name__ == "__main__":
    main()
