"""
NFL Player Performance Prediction Model
Uses XGBoost and LSTM to predict DFS points, ownership, and variance
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import pickle
from datetime import datetime, timedelta
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
try:
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, Concatenate
    from tensorflow.keras.optimizers import <PERSON>
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow not available - LSTM models disabled")
import joblib
from loguru import logger

from config import MODELS_DIR


class PlayerPerformanceModel:
    """Advanced ML model for predicting NFL player DFS performance"""
    
    def __init__(self, position: str = 'ALL'):
        self.position = position
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        
        # DFS scoring system (DraftKings)
        self.scoring_system = {
            'QB': {
                'pass_yds': 0.04,  # 1 pt per 25 yards
                'pass_tds': 4,
                'interceptions': -1,
                'rush_yds': 0.1,   # 1 pt per 10 yards
                'rush_tds': 6,
                'fumbles': -1
            },
            'RB': {
                'rush_yds': 0.1,
                'rush_tds': 6,
                'receptions': 1,
                'rec_yds': 0.1,
                'rec_tds': 6,
                'fumbles': -1
            },
            'WR': {
                'receptions': 1,
                'rec_yds': 0.1,
                'rec_tds': 6,
                'rush_yds': 0.1,
                'rush_tds': 6,
                'fumbles': -1
            },
            'TE': {
                'receptions': 1,
                'rec_yds': 0.1,
                'rec_tds': 6,
                'fumbles': -1
            },
            'K': {
                'fg_made': 3,
                'fg_40_49': 1,  # Bonus for 40-49 yard FGs
                'fg_50_plus': 2,  # Bonus for 50+ yard FGs
                'fg_missed': -1,
                'xp_made': 1,
                'xp_missed': -1
            },
            'DST': {
                'points_allowed_0': 10,
                'points_allowed_1_6': 7,
                'points_allowed_7_13': 4,
                'points_allowed_14_20': 1,
                'points_allowed_21_27': 0,
                'points_allowed_28_34': -1,
                'points_allowed_35_plus': -4,
                'sacks': 1,
                'interceptions': 2,
                'fumble_recoveries': 2,
                'safeties': 2,
                'tds': 6
            }
        }
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced features for ML model"""
        features_df = df.copy()
        
        # Sort by player and date
        features_df = features_df.sort_values(['player_id', 'game_date'])
        
        # Rolling averages (last 3, 5, 8 games)
        for window in [3, 5, 8]:
            for stat in ['fantasy_points', 'targets', 'carries', 'snaps', 'red_zone_targets']:
                if stat in features_df.columns:
                    features_df[f'{stat}_avg_{window}'] = (
                        features_df.groupby('player_id')[stat]
                        .rolling(window=window, min_periods=1)
                        .mean()
                        .reset_index(0, drop=True)
                    )
        
        # Opponent-adjusted stats
        if 'opponent_def_rank' in features_df.columns:
            features_df['adj_fantasy_points'] = (
                features_df['fantasy_points'] * 
                (features_df['opponent_def_rank'] / 16)  # Adjust for opponent strength
            )
        
        # Home/Away splits
        features_df['is_home'] = (features_df['home_team'] == features_df['team']).astype(int)
        
        # Weather impact
        if 'temperature' in features_df.columns:
            features_df['cold_weather'] = (features_df['temperature'] < 32).astype(int)
            features_df['wind_impact'] = np.where(
                features_df['wind_speed'] > 15, 
                features_df['wind_speed'] / 10, 
                0
            )
        
        # Vegas odds features
        if 'total_points' in features_df.columns:
            features_df['high_total'] = (features_df['total_points'] > 47).astype(int)
            features_df['implied_team_total'] = features_df['total_points'] / 2
            
            if 'spread' in features_df.columns:
                features_df['favorite'] = (features_df['spread'] < 0).astype(int)
                features_df['underdog'] = (features_df['spread'] > 3).astype(int)
        
        # Recency bias (more weight to recent games)
        features_df['games_ago'] = (
            features_df.groupby('player_id')
            .cumcount(ascending=False)
        )
        features_df['recency_weight'] = np.exp(-features_df['games_ago'] * 0.1)
        
        # Target share and air yards (for receivers)
        if 'targets' in features_df.columns and 'team_pass_attempts' in features_df.columns:
            features_df['target_share'] = (
                features_df['targets'] / features_df['team_pass_attempts']
            ).fillna(0)
        
        # Red zone usage
        if 'red_zone_targets' in features_df.columns:
            features_df['rz_target_share'] = (
                features_df['red_zone_targets'] / 
                features_df.groupby(['team', 'game_date'])['red_zone_targets'].transform('sum')
            ).fillna(0)
        
        # Injury/rest days
        features_df['days_rest'] = (
            features_df.groupby('player_id')['game_date']
            .diff()
            .dt.days
            .fillna(7)
        )
        
        # Consistency metrics
        for window in [5, 8]:
            if 'fantasy_points' in features_df.columns:
                rolling_std = (
                    features_df.groupby('player_id')['fantasy_points']
                    .rolling(window=window, min_periods=2)
                    .std()
                    .reset_index(0, drop=True)
                )
                features_df[f'consistency_{window}'] = 1 / (1 + rolling_std.fillna(0))
        
        return features_df
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Prepare data for training"""
        # Create features
        features_df = self.create_features(df)
        
        # Select feature columns
        feature_cols = [
            col for col in features_df.columns 
            if col not in ['player_id', 'player_name', 'game_date', 'fantasy_points', 'team', 'opponent']
            and not col.startswith('target_')  # Remove target variables
        ]
        
        # Handle missing values
        X = features_df[feature_cols].fillna(0)
        y = features_df['fantasy_points'].fillna(0)
        
        # Encode categorical variables
        categorical_cols = X.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if col not in self.encoders:
                self.encoders[col] = LabelEncoder()
                X[col] = self.encoders[col].fit_transform(X[col].astype(str))
            else:
                X[col] = self.encoders[col].transform(X[col].astype(str))
        
        return X.values, y.values, feature_cols
    
    def train_xgboost_model(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> xgb.XGBRegressor:
        """Train XGBoost model for point prediction"""
        # Time series split for validation
        tscv = TimeSeriesSplit(n_splits=3)
        
        best_model = None
        best_score = float('inf')
        
        # Hyperparameter tuning
        param_grid = [
            {
                'n_estimators': 200,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 0.1,
                'reg_lambda': 1.0
            },
            {
                'n_estimators': 300,
                'max_depth': 8,
                'learning_rate': 0.05,
                'subsample': 0.9,
                'colsample_bytree': 0.9,
                'reg_alpha': 0.05,
                'reg_lambda': 0.5
            }
        ]
        
        for params in param_grid:
            model = xgb.XGBRegressor(**params, random_state=42)
            
            # Cross-validation
            cv_scores = []
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                model.fit(X_train, y_train)
                y_pred = model.predict(X_val)
                cv_scores.append(mean_squared_error(y_val, y_pred))
            
            avg_score = np.mean(cv_scores)
            if avg_score < best_score:
                best_score = avg_score
                best_model = model
        
        # Train final model on all data
        best_model.fit(X, y)
        
        # Store feature importance
        self.feature_importance['xgboost'] = dict(zip(
            feature_names, 
            best_model.feature_importances_
        ))
        
        return best_model
    
    def create_lstm_model(self, sequence_length: int, n_features: int):
        """Create LSTM model for time series prediction"""
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available - cannot create LSTM model")
            return None

        # Input layers
        sequence_input = Input(shape=(sequence_length, n_features), name='sequence')
        static_input = Input(shape=(10,), name='static')  # Static features like position, team

        # LSTM layers
        lstm_out = LSTM(64, return_sequences=True, dropout=0.2)(sequence_input)
        lstm_out = LSTM(32, dropout=0.2)(lstm_out)

        # Dense layers for static features
        static_dense = Dense(16, activation='relu')(static_input)
        static_dense = Dropout(0.2)(static_dense)

        # Combine LSTM and static features
        combined = Concatenate()([lstm_out, static_dense])
        combined = Dense(32, activation='relu')(combined)
        combined = Dropout(0.3)(combined)
        combined = Dense(16, activation='relu')(combined)

        # Output layers
        points_output = Dense(1, activation='linear', name='points')(combined)
        variance_output = Dense(1, activation='softplus', name='variance')(combined)

        model = Model(
            inputs=[sequence_input, static_input],
            outputs=[points_output, variance_output]
        )

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss={'points': 'mse', 'variance': 'mse'},
            loss_weights={'points': 1.0, 'variance': 0.3}
        )

        return model
    
    def train_models(self, df: pd.DataFrame) -> Dict:
        """Train all prediction models"""
        logger.info(f"Training models for position: {self.position}")
        
        # Filter by position if specified
        if self.position != 'ALL':
            df = df[df['position'] == self.position]
        
        # Prepare training data
        X, y, feature_names = self.prepare_training_data(df)
        
        # Scale features
        self.scalers['features'] = StandardScaler()
        X_scaled = self.scalers['features'].fit_transform(X)
        
        # Train XGBoost model
        logger.info("Training XGBoost model...")
        self.models['xgboost'] = self.train_xgboost_model(X_scaled, y, feature_names)
        
        # Train LSTM model (if enough data and TensorFlow available)
        if len(df) > 1000 and TENSORFLOW_AVAILABLE:
            logger.info("Training LSTM model...")
            # This would require sequence preparation - simplified for now
            # self.models['lstm'] = self.train_lstm_model(df)
        elif not TENSORFLOW_AVAILABLE:
            logger.info("LSTM model skipped - TensorFlow not available")
        
        # Model evaluation
        y_pred_xgb = self.models['xgboost'].predict(X_scaled)
        
        metrics = {
            'xgboost': {
                'mse': mean_squared_error(y, y_pred_xgb),
                'mae': mean_absolute_error(y, y_pred_xgb),
                'r2': r2_score(y, y_pred_xgb)
            }
        }
        
        logger.info(f"Model performance: {metrics}")
        return metrics
    
    def predict(self, player_data: pd.DataFrame) -> Dict:
        """Make predictions for players"""
        if 'xgboost' not in self.models:
            raise ValueError("Models not trained yet!")
        
        # Prepare features
        features_df = self.create_features(player_data)
        feature_cols = [col for col in features_df.columns if col in self.feature_importance['xgboost']]
        X = features_df[feature_cols].fillna(0)
        
        # Encode categorical variables
        for col in X.select_dtypes(include=['object']).columns:
            if col in self.encoders:
                X[col] = self.encoders[col].transform(X[col].astype(str))
        
        # Scale features
        X_scaled = self.scalers['features'].transform(X.values)
        
        # Make predictions
        points_pred = self.models['xgboost'].predict(X_scaled)
        
        # Calculate prediction intervals (simplified)
        prediction_std = np.std(points_pred) * 0.5  # Rough estimate
        
        results = []
        for i, (_, player) in enumerate(player_data.iterrows()):
            results.append({
                'player_id': player['player_id'],
                'player_name': player.get('player_name', ''),
                'position': player.get('position', ''),
                'predicted_points': float(points_pred[i]),
                'floor': float(max(0, points_pred[i] - prediction_std)),
                'ceiling': float(points_pred[i] + prediction_std * 1.5),
                'confidence': min(1.0, 1.0 / (1.0 + prediction_std))
            })
        
        return results
    
    def save_models(self, filepath: str = None):
        """Save trained models"""
        if filepath is None:
            filepath = f"{MODELS_DIR}/player_model_{self.position.lower()}.pkl"
        
        model_data = {
            'models': self.models,
            'scalers': self.scalers,
            'encoders': self.encoders,
            'feature_importance': self.feature_importance,
            'position': self.position
        }
        
        joblib.dump(model_data, filepath)
        logger.info(f"Models saved to {filepath}")
    
    def load_models(self, filepath: str = None):
        """Load trained models"""
        if filepath is None:
            filepath = f"{MODELS_DIR}/player_model_{self.position.lower()}.pkl"
        
        model_data = joblib.load(filepath)
        self.models = model_data['models']
        self.scalers = model_data['scalers']
        self.encoders = model_data['encoders']
        self.feature_importance = model_data['feature_importance']
        self.position = model_data['position']
        
        logger.info(f"Models loaded from {filepath}")


# Example usage and testing
if __name__ == "__main__":
    # Create sample data for testing
    np.random.seed(42)
    
    sample_data = []
    for player_id in range(1, 101):  # 100 players
        for week in range(1, 18):  # 17 weeks
            sample_data.append({
                'player_id': f'player_{player_id}',
                'player_name': f'Player {player_id}',
                'position': np.random.choice(['QB', 'RB', 'WR', 'TE']),
                'game_date': pd.Timestamp('2024-09-01') + pd.Timedelta(weeks=week),
                'fantasy_points': np.random.normal(10, 5),
                'targets': np.random.poisson(5),
                'carries': np.random.poisson(3),
                'snaps': np.random.normal(50, 15),
                'team': np.random.choice(['KC', 'BUF', 'SF', 'DAL']),
                'opponent': np.random.choice(['KC', 'BUF', 'SF', 'DAL']),
                'home_team': np.random.choice(['KC', 'BUF', 'SF', 'DAL']),
                'total_points': np.random.normal(45, 5),
                'spread': np.random.normal(0, 3),
                'temperature': np.random.normal(65, 15),
                'wind_speed': np.random.exponential(5)
            })
    
    df = pd.DataFrame(sample_data)
    
    # Train model
    model = PlayerPerformanceModel(position='ALL')
    metrics = model.train_models(df)
    
    print("Model training completed!")
    print(f"Performance metrics: {metrics}")
    
    # Test prediction
    test_data = df.tail(10)
    predictions = model.predict(test_data)
    
    print("\nSample predictions:")
    for pred in predictions[:5]:
        print(f"{pred['player_name']}: {pred['predicted_points']:.1f} points "
              f"(Floor: {pred['floor']:.1f}, Ceiling: {pred['ceiling']:.1f})")
