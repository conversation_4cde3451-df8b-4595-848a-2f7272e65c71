"""
Main entry point for NFL DFS AI System
Run this to start data collection and test all components
"""
import asyncio
import pandas as pd
from datetime import datetime
from loguru import logger

from src.data_collection.espn_api import ESPNClient
from src.data_collection.weather_api import WeatherClient
from src.data_collection.odds_api import OddsClient
from config import *

# Configure logging
logger.add(
    f"{LOGS_DIR}/nfl_dfs_{datetime.now().strftime('%Y%m%d')}.log",
    format=LOG_FORMAT,
    level=LOG_LEVEL,
    rotation="1 day"
)


async def test_data_collection():
    """Test all data collection APIs"""
    logger.info("Starting NFL DFS AI System - Data Collection Test")
    
    # Initialize clients
    espn_client = ESPNClient()
    weather_client = WeatherClient()
    odds_client = OddsClient()
    
    try:
        # Test ESPN API
        logger.info("Testing ESPN API...")
        current_week = await espn_client.get_current_week()
        logger.info(f"Current NFL week: {current_week}")
        
        teams = await espn_client.get_teams()
        logger.info(f"Found {len(teams)} NFL teams")
        
        schedule = await espn_client.get_schedule(week=current_week)
        logger.info(f"Found {len(schedule)} games this week")
        
        if not schedule.empty:
            print("\n📅 This Week's Games:")
            for _, game in schedule.head().iterrows():
                print(f"  {game['away_team']} @ {game['home_team']} - {game['date']}")
        
        # Test Weather API (if API key provided)
        if WEATHER_API_KEY:
            logger.info("Testing Weather API...")
            
            if not schedule.empty:
                weather_data = await weather_client.get_game_weather(schedule.head(3))
                logger.info(f"Got weather for {len(weather_data)} games")
                
                if not weather_data.empty:
                    print("\n🌤️ Weather Conditions:")
                    for _, weather in weather_data.iterrows():
                        if weather['is_dome']:
                            print(f"  {weather['team']}: Indoor (Dome)")
                        else:
                            print(f"  {weather['team']}: {weather['temperature']}°F, "
                                  f"Wind: {weather['wind_speed']} mph, {weather['conditions']}")
        else:
            logger.warning("Weather API key not provided - skipping weather test")
        
        # Test Odds API (if API key provided)
        if ODDS_API_KEY:
            logger.info("Testing Odds API...")
            odds_data = await odds_client.get_nfl_odds(['spreads', 'totals'])
            logger.info(f"Found {len(odds_data)} odds entries")
            
            if not odds_data.empty:
                processed_odds = odds_client.process_odds_for_dfs(odds_data)
                logger.info(f"Processed odds for {len(processed_odds)} games")
                
                if not processed_odds.empty:
                    print("\n💰 Vegas Lines:")
                    for _, game in processed_odds.head(3).iterrows():
                        total = game.get('total_points', 'N/A')
                        spread = game.get('home_spread', 'N/A')
                        print(f"  {game['away_team']} @ {game['home_team']}: "
                              f"Total: {total}, Spread: {spread}")
        else:
            logger.warning("Odds API key not provided - skipping odds test")
        
        # Test player data
        logger.info("Testing player data collection...")
        players = await espn_client.get_player_stats()
        logger.info(f"Found {len(players)} players")
        
        if not players.empty:
            qbs = players[players['position'] == 'QB'].head(5)
            print(f"\n🏈 Sample QBs:")
            for _, player in qbs.iterrows():
                print(f"  {player['name']} ({player['team']})")
        
        logger.info("✅ Data collection test completed successfully!")
        
        return {
            'teams': teams,
            'schedule': schedule,
            'players': players,
            'weather': weather_data if WEATHER_API_KEY else pd.DataFrame(),
            'odds': processed_odds if ODDS_API_KEY else pd.DataFrame()
        }
        
    except Exception as e:
        logger.error(f"Error in data collection test: {e}")
        raise


async def main():
    """Main function"""
    print("🏈 NFL DFS AI System")
    print("=" * 50)
    
    # Create directories
    import os
    for directory in [DATA_DIR, MODELS_DIR, LOGS_DIR, LINEUPS_DIR]:
        os.makedirs(directory, exist_ok=True)
    
    # Run data collection test
    try:
        data = await test_data_collection()
        
        print(f"\n📊 Data Collection Summary:")
        print(f"  Teams: {len(data['teams'])}")
        print(f"  Games this week: {len(data['schedule'])}")
        print(f"  Players: {len(data['players'])}")
        print(f"  Weather data: {len(data['weather'])}")
        print(f"  Odds data: {len(data['odds'])}")
        
        print(f"\n🔑 API Status:")
        print(f"  ESPN API: ✅ Working")
        print(f"  Weather API: {'✅ Working' if WEATHER_API_KEY else '❌ No API key'}")
        print(f"  Odds API: {'✅ Working' if ODDS_API_KEY else '❌ No API key'}")
        
        print(f"\n🚀 Next Steps:")
        print(f"  1. Add API keys to .env file")
        print(f"  2. Set up database (next task)")
        print(f"  3. Build feature engineering pipeline")
        print(f"  4. Train ML models")
        print(f"  5. Create lineup optimization")
        
    except Exception as e:
        logger.error(f"System test failed: {e}")
        print(f"❌ System test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
