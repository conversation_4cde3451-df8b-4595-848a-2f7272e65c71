"""
College Football Betting Data Collection
Real-time odds, line movements, and sharp money tracking
"""
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
from typing import Dict, List, Optional
from loguru import logger

from config import ODDS_API_KEY


class CFBBettingData:
    """Collect and analyze college football betting data"""
    
    def __init__(self, api_key: str = ODDS_API_KEY):
        self.api_key = api_key
        self.base_url = "https://api.the-odds-api.com/v4"
        self.session = requests.Session()
        
        # CFB team mappings
        self.team_mappings = {
            'Alabama': 'ALA', 'Georgia': 'UGA', 'Michigan': 'MICH', 'Ohio State': 'OSU',
            'Texas': 'TEX', 'Oklahoma': 'OU', 'Oregon': 'ORE', 'Washington': 'WASH',
            'Notre Dame': 'ND', 'USC': 'USC', 'Cincinnati': 'CIN', 'UCF': 'UCF',
            '<PERSON>lemson': 'CLEM', 'Florida State': 'FSU', 'Miami': 'MIA', 'Penn State': 'PSU'
        }
        
        # Conference mappings for context
        self.conferences = {
            'SEC': ['Alabama', 'Georgia', 'Florida', 'Tennessee', 'LSU', 'Auburn', 'Arkansas', 'Mississippi State', 'Ole Miss', 'Missouri', 'South Carolina', 'Kentucky', 'Vanderbilt', 'Texas A&M'],
            'Big Ten': ['Michigan', 'Ohio State', 'Penn State', 'Wisconsin', 'Iowa', 'Minnesota', 'Illinois', 'Northwestern', 'Indiana', 'Purdue', 'Michigan State', 'Nebraska', 'Maryland', 'Rutgers'],
            'Big 12': ['Texas', 'Oklahoma', 'Oklahoma State', 'Baylor', 'TCU', 'Kansas', 'Kansas State', 'Iowa State', 'Texas Tech', 'West Virginia'],
            'Pac-12': ['Oregon', 'Washington', 'USC', 'UCLA', 'Stanford', 'California', 'Arizona', 'Arizona State', 'Utah', 'Colorado', 'Washington State', 'Oregon State'],
            'ACC': ['Clemson', 'Florida State', 'Miami', 'North Carolina', 'NC State', 'Virginia', 'Virginia Tech', 'Duke', 'Wake Forest', 'Georgia Tech', 'Boston College', 'Syracuse', 'Pittsburgh', 'Louisville']
        }
    
    async def get_cfb_odds(self, markets: List[str] = None) -> pd.DataFrame:
        """Get college football odds from multiple sportsbooks"""
        if markets is None:
            markets = ['spreads', 'totals', 'h2h']
        
        all_odds = []
        
        try:
            url = f"{self.base_url}/sports/americanfootball_ncaaf/odds"
            params = {
                'apiKey': self.api_key,
                'regions': 'us',
                'markets': ','.join(markets),
                'oddsFormat': 'american',
                'dateFormat': 'iso'
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            for game in data:
                game_info = {
                    'game_id': game['id'],
                    'sport': game['sport_key'],
                    'commence_time': game['commence_time'],
                    'home_team': game['home_team'],
                    'away_team': game['away_team']
                }
                
                # Process each sportsbook
                for bookmaker in game.get('bookmakers', []):
                    bookmaker_name = bookmaker['key']
                    
                    for market in bookmaker.get('markets', []):
                        market_type = market['key']
                        
                        for outcome in market.get('outcomes', []):
                            odds_entry = game_info.copy()
                            odds_entry.update({
                                'bookmaker': bookmaker_name,
                                'market': market_type,
                                'team': outcome['name'],
                                'odds': outcome['price']
                            })
                            
                            if market_type == 'spreads':
                                odds_entry['spread'] = outcome.get('point', 0)
                            elif market_type == 'totals':
                                odds_entry['total'] = outcome.get('point', 0)
                                odds_entry['total_type'] = outcome['name']  # Over/Under
                            
                            all_odds.append(odds_entry)
            
            logger.info(f"Retrieved {len(all_odds)} CFB odds entries")
            return pd.DataFrame(all_odds)
            
        except Exception as e:
            logger.error(f"Error getting CFB odds: {e}")
            return pd.DataFrame()
    
    def calculate_line_movement(self, current_odds: pd.DataFrame, historical_odds: pd.DataFrame = None) -> pd.DataFrame:
        """Calculate line movement and identify sharp action"""
        
        if historical_odds is None or historical_odds.empty:
            # For demo, simulate historical data
            historical_odds = current_odds.copy()
            historical_odds['odds'] = historical_odds['odds'] + np.random.randint(-20, 20, len(historical_odds))
            historical_odds['spread'] = historical_odds.get('spread', 0) + np.random.uniform(-1, 1, len(historical_odds))
        
        movement_data = []
        
        # Group by game and market
        for (game_id, market), group in current_odds.groupby(['game_id', 'market']):
            if market == 'spreads':
                # Calculate spread movement
                current_spread = group['spread'].mean()
                historical_spread = historical_odds[
                    (historical_odds['game_id'] == game_id) & 
                    (historical_odds['market'] == market)
                ]['spread'].mean() if not historical_odds.empty else current_spread
                
                movement = current_spread - historical_spread
                
                movement_data.append({
                    'game_id': game_id,
                    'home_team': group['home_team'].iloc[0],
                    'away_team': group['away_team'].iloc[0],
                    'market': market,
                    'current_line': current_spread,
                    'opening_line': historical_spread,
                    'movement': movement,
                    'movement_direction': 'toward_home' if movement < 0 else 'toward_away',
                    'sharp_indicator': abs(movement) > 1.0  # Significant movement
                })
        
        return pd.DataFrame(movement_data)
    
    def identify_sharp_action(self, odds_df: pd.DataFrame, public_betting_data: Dict = None) -> List[Dict]:
        """Identify sharp money indicators"""
        
        sharp_plays = []
        
        # Group by game
        for game_id, game_odds in odds_df.groupby('game_id'):
            game_info = {
                'game_id': game_id,
                'home_team': game_odds['home_team'].iloc[0],
                'away_team': game_odds['away_team'].iloc[0],
                'commence_time': game_odds['commence_time'].iloc[0]
            }
            
            # Analyze spread movement
            spreads = game_odds[game_odds['market'] == 'spreads']
            if not spreads.empty:
                # Look for reverse line movement (RLM)
                # This would normally compare with public betting percentages
                avg_spread = spreads['spread'].mean()
                spread_variance = spreads['spread'].var()
                
                # Simulate sharp indicators
                sharp_indicators = {
                    'reverse_line_movement': spread_variance > 0.5,  # High variance = disagreement
                    'steam_move': abs(avg_spread) > 7,  # Large spread movement
                    'sharp_book_consensus': len(spreads) > 5,  # Multiple books moving
                    'low_public_high_money': np.random.random() > 0.7  # Simulated
                }
                
                sharp_score = sum(sharp_indicators.values())
                
                if sharp_score >= 2:  # Threshold for sharp play
                    sharp_plays.append({
                        **game_info,
                        'sharp_score': sharp_score,
                        'indicators': sharp_indicators,
                        'recommended_play': f"{'Away' if avg_spread > 0 else 'Home'} +{abs(avg_spread):.1f}",
                        'confidence': 'HIGH' if sharp_score >= 3 else 'MEDIUM'
                    })
        
        return sharp_plays
    
    def get_public_betting_percentages(self, games: List[str]) -> Dict:
        """Get public betting percentages (would integrate with Action Network or similar)"""
        
        # For demo, simulate realistic public betting patterns
        public_data = {}
        
        for game in games:
            # Public tends to bet favorites and overs
            favorite_pct = np.random.uniform(60, 85)  # Public loves favorites
            over_pct = np.random.uniform(55, 75)      # Public loves overs
            
            public_data[game] = {
                'spread_public_pct': favorite_pct,
                'total_public_pct': over_pct,
                'money_public_pct': favorite_pct + np.random.uniform(-5, 5)
            }
        
        return public_data
    
    def calculate_betting_value(self, odds_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate betting value and expected value"""
        
        value_data = []
        
        for _, row in odds_df.iterrows():
            american_odds = row['odds']
            
            # Convert to implied probability
            if american_odds > 0:
                implied_prob = 100 / (american_odds + 100)
            else:
                implied_prob = abs(american_odds) / (abs(american_odds) + 100)
            
            # Estimate true probability (this would use your model)
            # For demo, add some noise to implied probability
            true_prob = implied_prob + np.random.uniform(-0.1, 0.1)
            true_prob = max(0.1, min(0.9, true_prob))  # Clamp between 10-90%
            
            # Calculate expected value
            if american_odds > 0:
                payout = american_odds / 100
            else:
                payout = 100 / abs(american_odds)
            
            expected_value = (true_prob * payout) - (1 - true_prob)
            
            value_data.append({
                **row.to_dict(),
                'implied_probability': implied_prob,
                'true_probability': true_prob,
                'expected_value': expected_value,
                'value_rating': 'STRONG' if expected_value > 0.1 else 'GOOD' if expected_value > 0.05 else 'FAIR' if expected_value > 0 else 'POOR'
            })
        
        return pd.DataFrame(value_data)
    
    def get_injury_reports(self, teams: List[str]) -> Dict:
        """Get injury reports for teams (would integrate with ESPN or similar)"""
        
        # Simulate injury data
        injury_data = {}
        
        for team in teams:
            # Random injury scenarios
            injuries = []
            
            if np.random.random() < 0.3:  # 30% chance of key injury
                position = np.random.choice(['QB', 'RB', 'WR', 'OL'])
                status = np.random.choice(['Questionable', 'Doubtful', 'Out'])
                injuries.append(f"{position}1 - {status}")
            
            if np.random.random() < 0.2:  # 20% chance of secondary injury
                position = np.random.choice(['LB', 'DB', 'DL'])
                status = np.random.choice(['Questionable', 'Probable'])
                injuries.append(f"{position}2 - {status}")
            
            injury_data[team] = injuries
        
        return injury_data
    
    def get_weather_impact(self, games: List[Dict]) -> Dict:
        """Get weather impact for outdoor games"""
        
        weather_data = {}
        
        for game in games:
            game_id = game.get('game_id', '')
            
            # Simulate weather conditions
            conditions = np.random.choice([
                'Clear', 'Partly Cloudy', 'Overcast', 'Light Rain', 'Heavy Rain', 'Snow', 'Wind'
            ], p=[0.4, 0.2, 0.15, 0.1, 0.05, 0.05, 0.05])
            
            temp = np.random.randint(35, 75)
            wind = np.random.randint(0, 25)
            
            # Calculate impact
            total_impact = 0
            if conditions in ['Heavy Rain', 'Snow']:
                total_impact = -3  # Favor under
            elif conditions in ['Light Rain', 'Wind'] or wind > 15:
                total_impact = -1.5
            
            weather_data[game_id] = {
                'conditions': conditions,
                'temperature': temp,
                'wind_speed': wind,
                'total_impact': total_impact,
                'recommendation': 'UNDER' if total_impact < -1 else 'NEUTRAL'
            }
        
        return weather_data


# Example usage and testing
if __name__ == "__main__":
    async def test_cfb_data():
        cfb_data = CFBBettingData()
        
        print("🏈 College Football Betting Data Test")
        print("=" * 50)
        
        # Test odds collection
        odds_df = await cfb_data.get_cfb_odds()
        print(f"Retrieved {len(odds_df)} odds entries")
        
        if not odds_df.empty:
            # Test line movement analysis
            movement_df = cfb_data.calculate_line_movement(odds_df)
            print(f"Analyzed {len(movement_df)} games for line movement")
            
            # Test sharp action identification
            sharp_plays = cfb_data.identify_sharp_action(odds_df)
            print(f"Identified {len(sharp_plays)} potential sharp plays")
            
            # Test value calculation
            value_df = cfb_data.calculate_betting_value(odds_df.head(10))
            print(f"Calculated value for {len(value_df)} bets")
            
            # Show sample results
            if sharp_plays:
                print(f"\nSample Sharp Play:")
                play = sharp_plays[0]
                print(f"{play['away_team']} @ {play['home_team']}")
                print(f"Recommended: {play['recommended_play']}")
                print(f"Confidence: {play['confidence']}")
    
    asyncio.run(test_cfb_data())
