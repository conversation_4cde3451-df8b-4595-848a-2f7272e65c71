# NFL DFS AI System

An advanced AI-powered Daily Fantasy Sports system for NFL on DraftKings, leveraging machine learning, optimization algorithms, and comprehensive data sources to maximize DFS profitability.

## 🏈 Features

- **Comprehensive Data Collection**: ESPN API, Weather API, Vegas Odds, Injury Reports
- **Advanced ML Models**: Player performance prediction using XGBoost/LSTM
- **Lineup Optimization**: Mathematical optimization for multiple lineup generation
- **Real-time Monitoring**: Live injury tracking and lineup adjustments
- **Weather Impact Analysis**: Game condition effects on player performance
- **Vegas Odds Integration**: Betting lines for game flow prediction
- **Automated Entry Management**: Contest selection and lineup submission

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd sports

# Install dependencies
pip install -r requirements.txt
```

### 2. API Keys Setup

Copy `.env.example` to `.env` and add your API keys:

```bash
cp .env.example .env
```

Required API keys:
- **Weather API**: [OpenWeatherMap](https://openweathermap.org/api) (Free tier: 1000 calls/day)
- **Odds API**: [The Odds API](https://the-odds-api.com/) (Free tier: 500 calls/month)
- **FantasyData API**: [FantasyData](https://fantasydata.com/) (Paid - most comprehensive)

### 3. Test Data Collection

```bash
# Test ESPN API
python src/data_collection/espn_api.py

# Test Weather API
python src/data_collection/weather_api.py

# Test Odds API
python src/data_collection/odds_api.py
```

## 📊 Data Sources

### Primary Sources (Free)
- **ESPN API**: Player stats, schedules, team info
- **NFL.com**: Official statistics and depth charts
- **OpenWeatherMap**: Game weather conditions
- **The Odds API**: Vegas lines and totals

### Premium Sources (Paid)
- **FantasyData API**: Comprehensive DFS data, projections, ownership
- **DraftKings API**: Contest data, salaries, live scoring

## 🏗️ Architecture

```
src/
├── data_collection/     # API clients and data gathering
├── database/           # Database models and connections
├── feature_engineering/ # Statistical feature creation
├── models/             # ML prediction models
├── optimization/       # Lineup optimization engine
├── dfs_platform/       # DraftKings integration
└── dashboard/          # Web interface
```

## 🎯 DFS Strategy

### Weather Impact
- **Wind > 15 MPH**: Reduce passing game exposure
- **Rain/Snow**: Favor rushing attacks and unders
- **Dome Games**: Weather-neutral, focus on pace

### Vegas Odds Insights
- **High Totals (50+)**: Target skill position players
- **Large Spreads**: Favor winning team's players
- **Close Games**: Target both teams' top options

### Lineup Construction
- **Cash Games**: High floor, low variance players
- **GPP Tournaments**: High ceiling, contrarian plays
- **Stacking**: QB + WR/TE correlations

## 🔧 Configuration

Key settings in `config.py`:
- `SALARY_CAP`: DraftKings salary limit (50000)
- `MAX_LINEUPS`: Maximum lineups to generate (150)
- `MAX_PLAYER_EXPOSURE`: Player exposure limit (30%)
- `DATA_UPDATE_INTERVAL_MINUTES`: Data refresh rate (15)

## 📈 Performance Tracking

The system tracks:
- ROI by contest type
- Player projection accuracy
- Lineup performance metrics
- Model prediction confidence

## 🚨 Important Notes

### API Rate Limits
- ESPN API: No official limits, use responsibly
- Weather API: 1000 calls/day (free tier)
- Odds API: 500 calls/month (free tier)

### Legal Compliance
- Only use in states where DFS is legal
- Follow DraftKings terms of service
- Respect API usage policies

### Risk Management
- Never bet more than you can afford to lose
- Diversify across multiple contest types
- Track bankroll and ROI carefully

## 🛠️ Development Roadmap

- [x] Project setup and data sources
- [ ] Database architecture
- [ ] Data collection pipeline
- [ ] Feature engineering
- [ ] ML prediction models
- [ ] Lineup optimization
- [ ] DraftKings integration
- [ ] Web dashboard

## 📝 License

This project is for educational purposes. Use responsibly and in compliance with local laws and platform terms of service.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For questions or issues, please open a GitHub issue or contact the development team.

---

**Disclaimer**: This software is for educational and research purposes only. Daily Fantasy Sports involves risk, and past performance does not guarantee future results. Always gamble responsibly.
