"""
TEXAS A&M AGGIES BETTING MACHINE
WHOOP! Where Aggie Pride Meets Sharp Money
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
from datetime import datetime, timedelta
import base64
from io import BytesIO

# Page config with Aggie colors
st.set_page_config(
    page_title="Aggie Betting Machine", 
    page_icon="🏈", 
    layout="wide"
)

# Texas A&M Official Colors and Styling
AGGIE_MAROON = "#500000"
AGGIE_WHITE = "#FFFFFF"
AGGIE_GOLD = "#FFCC00"

# Custom CSS with Aggie branding
st.markdown(f"""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Oswald:wght@400;600;700&display=swap');
    
    .main {{ 
        background: linear-gradient(135deg, {AGGIE_MAROON} 0%, #722F37 50%, {AGGIE_MAROON} 100%);
        font-family: '<PERSON>', sans-serif;
    }}
    
    .aggie-header {{
        background: linear-gradient(45deg, {AGGIE_MAROON}, #722F37);
        color: {AGGIE_WHITE};
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        font-size: 3.5rem;
        font-weight: 700;
        text-shadow: 3px 3px 6px rgba(0,0,0,0.7);
        margin-bottom: 2rem;
        border: 3px solid {AGGIE_GOLD};
        box-shadow: 0 0 20px rgba(255,204,0,0.3);
    }}
    
    .whoop-banner {{
        background: {AGGIE_GOLD};
        color: {AGGIE_MAROON};
        padding: 1rem;
        text-align: center;
        font-size: 2rem;
        font-weight: 700;
        border-radius: 10px;
        margin: 1rem 0;
        animation: pulse 2s infinite;
    }}
    
    @keyframes pulse {{
        0% {{ transform: scale(1); }}
        50% {{ transform: scale(1.02); }}
        100% {{ transform: scale(1); }}
    }}
    
    .lock-play {{
        background: linear-gradient(135deg, #00c851, #007e33);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        border: 3px solid {AGGIE_GOLD};
        margin: 1rem 0;
        box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        animation: glow 3s ease-in-out infinite alternate;
    }}
    
    @keyframes glow {{
        from {{ box-shadow: 0 0 10px {AGGIE_GOLD}; }}
        to {{ box-shadow: 0 0 25px {AGGIE_GOLD}, 0 0 35px {AGGIE_GOLD}; }}
    }}
    
    .strong-play {{
        background: linear-gradient(135deg, #4285f4, #1a73e8);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 5px solid {AGGIE_GOLD};
        margin: 1rem 0;
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }}
    
    .value-play {{
        background: linear-gradient(135deg, {AGGIE_MAROON}, #722F37);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 5px solid {AGGIE_WHITE};
        margin: 1rem 0;
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }}
    
    .aggie-metric {{
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border: 2px solid {AGGIE_GOLD};
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        color: white;
        font-weight: 600;
    }}
    
    .bankroll-aggie {{
        background: {AGGIE_MAROON};
        color: {AGGIE_GOLD};
        padding: 1.5rem;
        border-radius: 15px;
        border: 3px solid {AGGIE_GOLD};
        font-family: 'Oswald', sans-serif;
        text-align: center;
        font-weight: 700;
        box-shadow: 0 0 15px rgba(255,204,0,0.5);
    }}
    
    .aggie-game-card {{
        background: rgba(255,255,255,0.05);
        backdrop-filter: blur(15px);
        border: 2px solid rgba(255,204,0,0.3);
        border-radius: 20px;
        padding: 2rem;
        margin: 1rem 0;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }}
    
    .gig-em {{
        color: {AGGIE_GOLD};
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }}
    
    .aggie-logo {{
        filter: drop-shadow(0 0 10px rgba(255,204,0,0.5));
    }}
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=300)
def get_aggie_betting_data():
    """Get comprehensive Texas A&M betting data and analysis"""
    
    # Current Texas A&M season data - September 2025
    aggie_data = {
        'team_stats': {
            'record': '3-0',
            'conference_record': '1-0 SEC',
            'ranking': '#12 AP Poll',
            'offensive_ranking': '#18 Total Offense',
            'defensive_ranking': '#8 Total Defense',
            'home_record': '2-0',
            'road_record': '1-0',
            'ats_record': '2-1 ATS',
            'over_under': '1-2 O/U',
            'last_5_games': 'W-W-W (season start)'
        },

        'current_game': {
            'opponent': 'Arkansas Razorbacks',
            'date': 'September 28, 2025',
            'time': '2:30 PM CT',
            'location': 'Kyle Field, College Station, TX',
            'tv': 'CBS',
            'weather': 'Partly cloudy, 82°F',
            'rivalry': 'Southwest Classic renewed',
            'implications': 'SEC West positioning, CFP implications'
        },
        
        'betting_markets': {
            'spread': {
                'line': 'A&M -7.5',
                'juice': '-110/-110',
                'opening_line': 'A&M -6.5',
                'movement': '+1.0 toward A&M',
                'public_bet': 'A&M 73%',
                'sharp_action': 'Arkansas +7.5',
                'expected_value': 0.11,
                'probability': 0.62,
                'recommendation': 'Arkansas +7.5',
                'confidence': 'STRONG',
                'reasoning': 'Sharp reverse line movement, Arkansas desperate after 0-3 start, public overvaluing A&M hot start'
            },
            
            'moneyline': {
                'a&m_ml': -165,
                'opponent_ml': +140,
                'expected_value': -0.02,
                'probability': 0.62,
                'recommendation': 'PASS',
                'confidence': 'NONE',
                'reasoning': 'No value in current pricing'
            },
            
            'total': {
                'line': 'O/U 58.5',
                'juice': '-110/-110',
                'opening_total': 60.5,
                'movement': '-2.0 toward Under',
                'public_bet': 'Over 64%',
                'sharp_action': 'Under 58.5',
                'expected_value': 0.14,
                'probability': 0.68,
                'recommendation': 'Under 58.5',
                'confidence': 'LOCK',
                'reasoning': 'September heat slowing pace, both defenses strong, Arkansas struggling offensively, sharp money hammering under'
            },
            
            'player_props': {
                'qb_passing_yards': {
                    'line': 'O/U 268.5',
                    'recommendation': 'Under 268.5',
                    'expected_value': 0.13,
                    'reasoning': 'Arkansas strong pass defense, A&M likely to run clock with lead'
                },
                'rb_rushing_yards': {
                    'line': 'O/U 95.5',
                    'recommendation': 'Over 95.5',
                    'expected_value': 0.12,
                    'reasoning': 'A&M ground game elite, Arkansas run defense vulnerable, game script favors rushing'
                }
            },

            'team_props': {
                'a&m_total_points': {
                    'line': 'O/U 32.5',
                    'recommendation': 'Under 32.5',
                    'expected_value': 0.09,
                    'reasoning': 'Arkansas defense improved, A&M may play conservative with big lead'
                }
            }
        },
        
        'parlay_opportunities': [
            {
                'name': 'Aggie September Special',
                'legs': ['Arkansas +7.5', 'Under 58.5', 'A&M QB Under 268.5 Pass Yds'],
                'odds': +580,
                'probability': 0.32,
                'expected_value': 0.86,
                'confidence': 'STRONG',
                'payout_1u': '$5.80'
            },
            {
                'name': 'SEC Week 4 Stack',
                'legs': ['Arkansas +7.5', 'Alabama -10.5', 'Georgia -6.5'],
                'odds': +420,
                'probability': 0.38,
                'expected_value': 0.60,
                'confidence': 'VALUE',
                'payout_1u': '$4.20'
            }
        ]
    }
    
    return aggie_data

def show_aggie_header():
    """Show Texas A&M branded header"""
    
    st.markdown(f'''
    <div class="aggie-header">
        🏈 TEXAS A&M AGGIES BETTING MACHINE 🏈
        <br><span style="font-size: 1.5rem;">WHOOP! GIG 'EM AGGIES!</span>
    </div>
    ''', unsafe_allow_html=True)
    
    st.markdown(f'''
    <div class="whoop-banner">
        🔥 SEPTEMBER 2025 • 3-0 START • SEC WEST LEADERS • 12TH MAN STRONG 🔥
    </div>
    ''', unsafe_allow_html=True)

def show_aggie_bankroll():
    """Show Aggie-themed bankroll management"""
    
    st.sidebar.markdown("### 🏈 AGGIE BANKROLL COMMAND")
    
    # Initialize Aggie bankroll
    if 'aggie_bankroll' not in st.session_state:
        st.session_state.aggie_bankroll = 1200.0  # Start with $1200 (12th Man tribute)
        st.session_state.aggie_wins = 0
        st.session_state.aggie_losses = 0
        st.session_state.biggest_win = 0.0
    
    current_bankroll = st.session_state.aggie_bankroll
    total_bets = st.session_state.aggie_wins + st.session_state.aggie_losses
    win_rate = (st.session_state.aggie_wins / max(1, total_bets)) * 100
    
    st.sidebar.markdown(f'''
    <div class="bankroll-aggie">
        <h3>💰 AGGIE WAR CHEST</h3>
        <h2>${current_bankroll:.2f}</h2>
        <p>Record: {st.session_state.aggie_wins}-{st.session_state.aggie_losses}</p>
        <p>Win Rate: {win_rate:.1f}%</p>
        <p>Biggest Win: ${st.session_state.biggest_win:.2f}</p>
        <p><span class="gig-em">GIG 'EM! 👍</span></p>
    </div>
    ''', unsafe_allow_html=True)
    
    # Aggie unit sizing
    st.sidebar.markdown("### 🎯 AGGIE UNIT SYSTEM")
    base_unit = st.sidebar.slider("Base Unit ($)", 25, 100, 50)  # Aggie-sized units
    max_play = st.sidebar.slider("Max Play (units)", 1, 5, 3)
    
    st.sidebar.markdown(f"**1 Unit = ${base_unit} (Aggie Standard)**")
    st.sidebar.markdown(f"**Max Play = ${base_unit * max_play} (12th Man Special)**")
    
    # Aggie Kelly Criterion
    st.sidebar.markdown("### 📊 AGGIE EDGE CALCULATOR")
    st.sidebar.markdown("*Based on Kyle Field home field advantage*")
    
    return base_unit, max_play

def show_aggie_game_analysis(aggie_data):
    """Show detailed Texas A&M game analysis"""
    
    current_game = aggie_data['current_game']
    team_stats = aggie_data['team_stats']
    
    st.markdown("## 🏈 AGGIE GAME BREAKDOWN")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown(f'''
        <div class="aggie-metric">
            <h3>🏟️ KYLE FIELD</h3>
            <h4>{current_game['opponent']}</h4>
            <p>{current_game['date']}</p>
            <p>{current_game['time']}</p>
            <p class="gig-em">HOME FIELD ADVANTAGE</p>
        </div>
        ''', unsafe_allow_html=True)
    
    with col2:
        st.markdown(f'''
        <div class="aggie-metric">
            <h3>📊 SEASON STATS</h3>
            <p><strong>{team_stats['record']}</strong></p>
            <p>{team_stats['conference_record']}</p>
            <p>{team_stats['ranking']}</p>
            <p class="gig-em">{team_stats['ats_record']}</p>
        </div>
        ''', unsafe_allow_html=True)
    
    with col3:
        st.markdown(f'''
        <div class="aggie-metric">
            <h3>🎯 RECENT FORM</h3>
            <p><strong>Last 5: {team_stats['last_5_games']}</strong></p>
            <p>Home: {team_stats['home_record']}</p>
            <p>Road: {team_stats['road_record']}</p>
            <p class="gig-em">O/U: {team_stats['over_under']}</p>
        </div>
        ''', unsafe_allow_html=True)

def show_aggie_betting_plays(aggie_data):
    """Show ranked betting plays for Texas A&M"""
    
    betting_markets = aggie_data['betting_markets']
    
    st.markdown("## 🔒 AGGIE BETTING PLAYS - RANKED BY VALUE")
    
    # Collect all plays and rank by expected value
    all_plays = []
    
    # Spread play
    spread = betting_markets['spread']
    all_plays.append({
        'type': 'SPREAD',
        'play': spread['recommendation'],
        'confidence': spread['confidence'],
        'expected_value': spread['expected_value'],
        'probability': spread['probability'],
        'reasoning': spread['reasoning'],
        'market': 'Point Spread'
    })
    
    # Total play
    total = betting_markets['total']
    all_plays.append({
        'type': 'TOTAL',
        'play': total['recommendation'],
        'confidence': total['confidence'],
        'expected_value': total['expected_value'],
        'probability': total['probability'],
        'reasoning': total['reasoning'],
        'market': 'Game Total'
    })
    
    # Player props
    for prop_name, prop_data in betting_markets['player_props'].items():
        all_plays.append({
            'type': 'PLAYER PROP',
            'play': prop_data['recommendation'],
            'confidence': 'VALUE',
            'expected_value': prop_data['expected_value'],
            'probability': 0.55,  # Estimated
            'reasoning': prop_data['reasoning'],
            'market': prop_name.replace('_', ' ').title()
        })
    
    # Sort by expected value
    all_plays.sort(key=lambda x: x['expected_value'], reverse=True)
    
    # Display plays by confidence level
    lock_plays = [p for p in all_plays if p['confidence'] == 'LOCK']
    strong_plays = [p for p in all_plays if p['confidence'] == 'STRONG']
    value_plays = [p for p in all_plays if p['confidence'] == 'VALUE']
    
    # Lock Plays
    if lock_plays:
        st.markdown("### 🔒 LOCK PLAYS - BET THE HOUSE")
        for play in lock_plays:
            roi_pct = play['expected_value'] * 100
            prob_pct = play['probability'] * 100
            
            st.markdown(f'''
            <div class="lock-play">
                <h3>🔒 AGGIE LOCK - {play['market']}</h3>
                <h2 class="gig-em">{play['play']}</h2>
                <p><strong>Expected ROI: +{roi_pct:.1f}%</strong></p>
                <p><strong>Win Probability: {prob_pct:.1f}%</strong></p>
                <p><strong>Reasoning:</strong> {play['reasoning']}</p>
                <p><strong>Recommended Bet:</strong> 3-5 Units (Max Play)</p>
            </div>
            ''', unsafe_allow_html=True)
    
    # Strong Plays
    if strong_plays:
        st.markdown("### 💪 STRONG PLAYS - HIGH CONFIDENCE")
        for play in strong_plays:
            roi_pct = play['expected_value'] * 100
            prob_pct = play['probability'] * 100
            
            st.markdown(f'''
            <div class="strong-play">
                <h3>💪 STRONG AGGIE PLAY - {play['market']}</h3>
                <h4>{play['play']}</h4>
                <p><strong>Expected ROI: +{roi_pct:.1f}%</strong></p>
                <p><strong>Win Probability: {prob_pct:.1f}%</strong></p>
                <p><strong>Reasoning:</strong> {play['reasoning']}</p>
                <p><strong>Recommended Bet:</strong> 2-3 Units</p>
            </div>
            ''', unsafe_allow_html=True)
    
    # Value Plays
    if value_plays:
        st.markdown("### 💎 VALUE PLAYS - SOLID OPPORTUNITIES")
        for play in value_plays:
            roi_pct = play['expected_value'] * 100
            prob_pct = play['probability'] * 100
            
            st.markdown(f'''
            <div class="value-play">
                <h3>💎 AGGIE VALUE - {play['market']}</h3>
                <h4>{play['play']}</h4>
                <p><strong>Expected ROI: +{roi_pct:.1f}%</strong></p>
                <p><strong>Win Probability: {prob_pct:.1f}%</strong></p>
                <p><strong>Reasoning:</strong> {play['reasoning']}</p>
                <p><strong>Recommended Bet:</strong> 1-2 Units</p>
            </div>
            ''', unsafe_allow_html=True)

def show_aggie_parlays(aggie_data):
    """Show Aggie parlay opportunities"""
    
    st.markdown("## 🚀 AGGIE PARLAY SPECIALS")
    st.markdown("**Multiply your Aggie pride with these correlated plays**")
    
    parlays = aggie_data['parlay_opportunities']
    
    for parlay in parlays:
        ev_pct = parlay['expected_value'] * 100
        prob_pct = parlay['probability'] * 100
        
        confidence_class = "lock-play" if parlay['confidence'] == 'LOCK' else "strong-play" if parlay['confidence'] == 'STRONG' else "value-play"
        
        st.markdown(f'''
        <div class="{confidence_class}">
            <h3>🚀 {parlay['name']} - {parlay['confidence']}</h3>
            <h4>Odds: {parlay['odds']} | Payout: {parlay['payout_1u']} per unit</h4>
            <p><strong>Legs:</strong></p>
            <ul>
        ''', unsafe_allow_html=True)
        
        for leg in parlay['legs']:
            st.markdown(f"<li>{leg}</li>", unsafe_allow_html=True)
        
        st.markdown(f'''
            </ul>
            <p><strong>Expected Value: +{ev_pct:.1f}%</strong></p>
            <p><strong>Win Probability: {prob_pct:.1f}%</strong></p>
            <p><strong>Recommended Bet:</strong> {'2-3 Units' if parlay['confidence'] == 'STRONG' else '1 Unit'}</p>
        </div>
        ''', unsafe_allow_html=True)

def show_aggie_analytics(aggie_data):
    """Show advanced Aggie betting analytics"""
    
    st.markdown("## 📊 AGGIE ANALYTICS COMMAND CENTER")
    
    # Create probability vs payout chart
    betting_markets = aggie_data['betting_markets']
    
    plays_data = []
    plays_data.append({
        'Play': 'Spread',
        'Probability': betting_markets['spread']['probability'],
        'Expected_Value': betting_markets['spread']['expected_value'],
        'Market': 'Point Spread'
    })
    plays_data.append({
        'Play': 'Total',
        'Probability': betting_markets['total']['probability'], 
        'Expected_Value': betting_markets['total']['expected_value'],
        'Market': 'Game Total'
    })
    
    df = pd.DataFrame(plays_data)
    
    # Create scatter plot
    fig = px.scatter(
        df,
        x='Probability',
        y='Expected_Value',
        size=[abs(x) * 100 for x in df['Expected_Value']],
        color='Market',
        hover_data=['Play'],
        title="Aggie Betting Opportunities - Probability vs Expected Value",
        labels={'Probability': 'Win Probability', 'Expected_Value': 'Expected Value (ROI)'},
        color_discrete_map={'Point Spread': AGGIE_MAROON, 'Game Total': AGGIE_GOLD}
    )
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font_color='white'
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Show betting trends
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🏈 AGGIE BETTING TRENDS")
        st.markdown(f"""
        - **Home ATS**: 2-0 (Kyle Field Magic continues)
        - **As Favorite**: 2-1 ATS (early season)
        - **As Underdog**: 0-0 ATS
        - **SEC Games**: 1-0 ATS
        - **September**: 2-1 ATS
        - **vs Arkansas**: 4-1 ATS L5 meetings
        """)
    
    with col2:
        st.markdown("### 📈 MARKET ANALYSIS")
        st.markdown(f"""
        - **Public Bias**: 73% on A&M spread
        - **Sharp Action**: Contrarian on Arkansas
        - **Line Movement**: +1.0 toward A&M
        - **Total Movement**: -2.0 toward Under
        - **Weather Impact**: Moderate (82°F, humid)
        - **Motivation**: High (SEC positioning + CFP hopes)
        """)

def main():
    """Main Aggie betting interface"""
    
    # Aggie header
    show_aggie_header()
    
    # Load Aggie data
    aggie_data = get_aggie_betting_data()
    
    # Sidebar bankroll
    base_unit, max_play = show_aggie_bankroll()
    
    # Main content
    show_aggie_game_analysis(aggie_data)
    
    # Betting plays
    show_aggie_betting_plays(aggie_data)
    
    # Parlays
    show_aggie_parlays(aggie_data)
    
    # Analytics
    show_aggie_analytics(aggie_data)
    
    # Aggie footer
    st.markdown("---")
    st.markdown(f'''
    <div style="text-align: center; color: {AGGIE_GOLD}; font-size: 1.5rem; font-weight: 700;">
        🏈 WHOOP! GIG 'EM AGGIES! 🏈<br>
        <span style="font-size: 1rem;">From here, it will be done. Bet responsibly, Ags.</span>
    </div>
    ''', unsafe_allow_html=True)

if __name__ == "__main__":
    main()
