"""
NFL DFS Ownership Prediction Model
Predicts player ownership percentages for tournament strategy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, accuracy_score
import joblib
from loguru import logger

from config import MODELS_DIR


class OwnershipModel:
    """Predict player ownership percentages in DFS contests"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        
        # Ownership tiers for classification
        self.ownership_tiers = {
            'chalk': 25,      # 25%+ ownership
            'popular': 15,    # 15-25% ownership  
            'medium': 8,      # 8-15% ownership
            'low': 3,         # 3-8% ownership
            'contrarian': 0   # <3% ownership
        }
    
    def create_ownership_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create features that predict ownership"""
        features_df = df.copy()
        
        # Salary-based features
        if 'salary' in features_df.columns:
            features_df['salary_rank'] = features_df.groupby(['position', 'week'])['salary'].rank(pct=True)
            features_df['salary_per_point'] = features_df['salary'] / (features_df['projected_points'] + 0.1)
            features_df['value_score'] = features_df['projected_points'] / (features_df['salary'] / 1000)
        
        # Projection-based features
        if 'projected_points' in features_df.columns:
            features_df['proj_rank'] = features_df.groupby(['position', 'week'])['projected_points'].rank(pct=True)
            features_df['proj_ceiling_ratio'] = features_df['projected_ceiling'] / features_df['projected_points']
            features_df['proj_floor_ratio'] = features_df['projected_floor'] / features_df['projected_points']
        
        # Team popularity features
        team_popularity = {
            'KC': 0.9, 'BUF': 0.8, 'SF': 0.8, 'DAL': 0.9, 'GB': 0.8,
            'NE': 0.7, 'PIT': 0.7, 'SEA': 0.7, 'DEN': 0.6, 'LV': 0.6,
            'LAR': 0.6, 'LAC': 0.6, 'MIN': 0.6, 'ATL': 0.5, 'NO': 0.6,
            'TB': 0.7, 'MIA': 0.6, 'CIN': 0.6, 'BAL': 0.7, 'CLE': 0.5,
            'TEN': 0.4, 'IND': 0.5, 'HOU': 0.5, 'JAX': 0.4, 'CAR': 0.4,
            'NYG': 0.6, 'NYJ': 0.5, 'PHI': 0.6, 'WAS': 0.5, 'CHI': 0.5,
            'DET': 0.5, 'ARI': 0.4
        }
        features_df['team_popularity'] = features_df['team'].map(team_popularity).fillna(0.5)
        
        # Game environment features
        if 'total_points' in features_df.columns:
            features_df['high_total_game'] = (features_df['total_points'] > 47).astype(int)
            features_df['primetime_game'] = features_df['game_time'].dt.hour.isin([17, 20]).astype(int)
        
        # Position-specific features
        features_df['qb_stack_appeal'] = 0
        features_df['contrarian_appeal'] = 0
        
        if 'position' in features_df.columns:
            # QB stacking appeal (high-total games)
            qb_mask = features_df['position'] == 'QB'
            features_df.loc[qb_mask, 'qb_stack_appeal'] = (
                features_df.loc[qb_mask, 'total_points'] > 47
            ).astype(int)
            
            # Contrarian appeal (good players on bad teams)
            features_df['contrarian_appeal'] = (
                (features_df['proj_rank'] > 0.6) & 
                (features_df['team_popularity'] < 0.5)
            ).astype(int)
        
        # Vegas-based features
        if 'spread' in features_df.columns:
            features_df['favorite'] = (features_df['spread'] < -3).astype(int)
            features_df['big_underdog'] = (features_df['spread'] > 7).astype(int)
        
        # Injury/news impact
        features_df['news_impact'] = 0  # Would be populated from news sentiment
        features_df['injury_concern'] = 0  # Would be populated from injury reports
        
        # Historical ownership patterns
        features_df['avg_ownership_last_3'] = 0  # Would be calculated from historical data
        
        # Slate size impact
        features_df['slate_size'] = len(features_df)  # Number of players on slate
        features_df['position_depth'] = features_df.groupby('position')['position'].transform('count')
        
        return features_df
    
    def prepare_ownership_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Prepare data for ownership prediction"""
        features_df = self.create_ownership_features(df)
        
        # Select feature columns
        feature_cols = [
            'salary_rank', 'salary_per_point', 'value_score',
            'proj_rank', 'proj_ceiling_ratio', 'proj_floor_ratio',
            'team_popularity', 'high_total_game', 'primetime_game',
            'qb_stack_appeal', 'contrarian_appeal', 'favorite', 'big_underdog',
            'slate_size', 'position_depth'
        ]
        
        # Filter existing columns
        feature_cols = [col for col in feature_cols if col in features_df.columns]
        
        X = features_df[feature_cols].fillna(0)
        y = features_df['ownership_pct'].fillna(5)  # Default 5% ownership
        
        return X.values, y.values, feature_cols
    
    def train_ownership_models(self, df: pd.DataFrame) -> Dict:
        """Train ownership prediction models"""
        logger.info("Training ownership prediction models...")
        
        X, y, feature_names = self.prepare_ownership_data(df)
        
        # Scale features
        self.scalers['ownership'] = StandardScaler()
        X_scaled = self.scalers['ownership'].fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        # Train regression model for exact ownership prediction
        self.models['ownership_regressor'] = RandomForestRegressor(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            random_state=42
        )
        self.models['ownership_regressor'].fit(X_train, y_train)
        
        # Train classification model for ownership tiers
        y_tiers = self.convert_to_tiers(y_train)
        self.models['ownership_classifier'] = RandomForestRegressor(
            n_estimators=100,
            max_depth=8,
            random_state=42
        )
        self.models['ownership_classifier'].fit(X_train, y_tiers)
        
        # Evaluate models
        y_pred_reg = self.models['ownership_regressor'].predict(X_test)
        y_pred_class = self.models['ownership_classifier'].predict(X_test)
        y_test_tiers = self.convert_to_tiers(y_test)
        
        metrics = {
            'regression_mae': mean_absolute_error(y_test, y_pred_reg),
            'classification_mae': mean_absolute_error(y_test_tiers, y_pred_class)
        }
        
        # Store feature importance
        self.feature_importance['ownership'] = dict(zip(
            feature_names,
            self.models['ownership_regressor'].feature_importances_
        ))
        
        logger.info(f"Ownership model performance: {metrics}")
        return metrics
    
    def convert_to_tiers(self, ownership_pct: np.ndarray) -> np.ndarray:
        """Convert ownership percentages to tier numbers"""
        tiers = np.zeros_like(ownership_pct)
        tiers[ownership_pct >= 25] = 4  # chalk
        tiers[(ownership_pct >= 15) & (ownership_pct < 25)] = 3  # popular
        tiers[(ownership_pct >= 8) & (ownership_pct < 15)] = 2  # medium
        tiers[(ownership_pct >= 3) & (ownership_pct < 8)] = 1  # low
        tiers[ownership_pct < 3] = 0  # contrarian
        return tiers
    
    def predict_ownership(self, player_data: pd.DataFrame) -> List[Dict]:
        """Predict ownership for players"""
        if 'ownership_regressor' not in self.models:
            raise ValueError("Ownership models not trained yet!")
        
        # Prepare features
        features_df = self.create_ownership_features(player_data)
        feature_cols = list(self.feature_importance['ownership'].keys())
        X = features_df[feature_cols].fillna(0)
        
        # Scale features
        X_scaled = self.scalers['ownership'].transform(X.values)
        
        # Make predictions
        ownership_pred = self.models['ownership_regressor'].predict(X_scaled)
        tier_pred = self.models['ownership_classifier'].predict(X_scaled)
        
        results = []
        for i, (_, player) in enumerate(player_data.iterrows()):
            ownership_pct = max(0.1, min(100, ownership_pred[i]))  # Clamp between 0.1% and 100%
            tier = int(round(tier_pred[i]))
            
            tier_names = ['contrarian', 'low', 'medium', 'popular', 'chalk']
            tier_name = tier_names[min(tier, 4)]
            
            results.append({
                'player_id': player['player_id'],
                'player_name': player.get('player_name', ''),
                'position': player.get('position', ''),
                'predicted_ownership': float(ownership_pct),
                'ownership_tier': tier_name,
                'leverage_score': self.calculate_leverage(
                    player.get('projected_points', 0),
                    ownership_pct
                )
            })
        
        return results
    
    def calculate_leverage(self, projected_points: float, ownership_pct: float) -> float:
        """Calculate leverage score (points per ownership)"""
        if ownership_pct <= 0:
            return 0
        return projected_points / (ownership_pct / 100)
    
    def identify_contrarian_plays(self, predictions: List[Dict], min_points: float = 8) -> List[Dict]:
        """Identify high-leverage contrarian plays"""
        contrarian_plays = []
        
        for pred in predictions:
            if (pred['predicted_ownership'] < 5 and 
                pred.get('projected_points', 0) > min_points):
                
                contrarian_plays.append({
                    **pred,
                    'contrarian_score': pred['leverage_score'] * 
                                      (1 / (pred['predicted_ownership'] + 0.1))
                })
        
        # Sort by contrarian score
        contrarian_plays.sort(key=lambda x: x['contrarian_score'], reverse=True)
        return contrarian_plays
    
    def save_models(self, filepath: str = None):
        """Save ownership models"""
        if filepath is None:
            filepath = f"{MODELS_DIR}/ownership_model.pkl"
        
        model_data = {
            'models': self.models,
            'scalers': self.scalers,
            'feature_importance': self.feature_importance
        }
        
        joblib.dump(model_data, filepath)
        logger.info(f"Ownership models saved to {filepath}")
    
    def load_models(self, filepath: str = None):
        """Load ownership models"""
        if filepath is None:
            filepath = f"{MODELS_DIR}/ownership_model.pkl"
        
        model_data = joblib.load(filepath)
        self.models = model_data['models']
        self.scalers = model_data['scalers']
        self.feature_importance = model_data['feature_importance']
        
        logger.info(f"Ownership models loaded from {filepath}")


# Example usage
if __name__ == "__main__":
    # Create sample data
    np.random.seed(42)
    
    sample_data = []
    for i in range(200):
        sample_data.append({
            'player_id': f'player_{i}',
            'player_name': f'Player {i}',
            'position': np.random.choice(['QB', 'RB', 'WR', 'TE', 'K', 'DST']),
            'team': np.random.choice(['KC', 'BUF', 'SF', 'DAL', 'JAX', 'DET']),
            'salary': np.random.randint(4000, 12000),
            'projected_points': np.random.normal(10, 4),
            'projected_ceiling': np.random.normal(15, 5),
            'projected_floor': np.random.normal(6, 3),
            'total_points': np.random.normal(45, 5),
            'spread': np.random.normal(0, 4),
            'game_time': pd.Timestamp('2024-09-22 13:00:00'),
            'week': 3,
            'ownership_pct': np.random.exponential(8)  # Exponential distribution for ownership
        })
    
    df = pd.DataFrame(sample_data)
    
    # Train model
    model = OwnershipModel()
    metrics = model.train_ownership_models(df)
    
    print("Ownership model training completed!")
    print(f"Performance metrics: {metrics}")
    
    # Test predictions
    test_data = df.head(10)
    predictions = model.predict_ownership(test_data)
    
    print("\nOwnership predictions:")
    for pred in predictions:
        print(f"{pred['player_name']} ({pred['position']}): "
              f"{pred['predicted_ownership']:.1f}% - {pred['ownership_tier']}")
    
    # Find contrarian plays
    contrarian = model.identify_contrarian_plays(predictions)
    print(f"\nTop contrarian plays:")
    for play in contrarian[:3]:
        print(f"{play['player_name']}: {play['predicted_ownership']:.1f}% ownership, "
              f"Leverage: {play['leverage_score']:.2f}")
