"""
LIVE NFL DFS SCORING TRACKER
Track your lineups against REAL NFL games as they happen
"""
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
import json
from typing import Dict, List, Optional
from loguru import logger

class LiveNFLTracker:
    """Track live NFL games and calculate DFS points in real-time"""
    
    def __init__(self):
        self.espn_base = "https://site.api.espn.com/apis/site/v2/sports/football/nfl"
        self.active_lineups = {}
        self.live_scores = {}
        
        # DraftKings scoring system
        self.scoring = {
            'QB': {
                'passing_yards': 0.04,  # 1 pt per 25 yards
                'passing_tds': 4,
                'interceptions': -1,
                'rushing_yards': 0.1,   # 1 pt per 10 yards
                'rushing_tds': 6,
                'fumbles_lost': -1
            },
            'RB': {
                'rushing_yards': 0.1,
                'rushing_tds': 6,
                'receptions': 1,
                'receiving_yards': 0.1,
                'receiving_tds': 6,
                'fumbles_lost': -1
            },
            'WR': {
                'receptions': 1,
                'receiving_yards': 0.1,
                'receiving_tds': 6,
                'rushing_yards': 0.1,
                'rushing_tds': 6,
                'fumbles_lost': -1
            },
            'TE': {
                'receptions': 1,
                'receiving_yards': 0.1,
                'receiving_tds': 6,
                'fumbles_lost': -1
            },
            'K': {
                'extra_points': 1,
                'field_goals_made': 3,
                'field_goals_40_49': 1,  # Bonus
                'field_goals_50_plus': 2,  # Bonus
                'field_goals_missed': -1
            },
            'DST': {
                'points_allowed_0': 10,
                'points_allowed_1_6': 7,
                'points_allowed_7_13': 4,
                'points_allowed_14_20': 1,
                'points_allowed_21_27': 0,
                'points_allowed_28_34': -1,
                'points_allowed_35_plus': -4,
                'sacks': 1,
                'interceptions': 2,
                'fumble_recoveries': 2,
                'safeties': 2,
                'defensive_tds': 6,
                'return_tds': 6
            }
        }
    
    async def get_live_games(self) -> Dict:
        """Get live NFL games and scores"""
        try:
            url = f"{self.espn_base}/scoreboard"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            live_games = {}
            
            for event in data.get('events', []):
                game_id = event['id']
                status = event['status']['type']['name']
                
                # Only track live or completed games
                if status in ['STATUS_IN_PROGRESS', 'STATUS_FINAL', 'STATUS_HALFTIME']:
                    game_info = {
                        'game_id': game_id,
                        'status': status,
                        'clock': event['status'].get('displayClock', ''),
                        'period': event['status'].get('period', 0),
                        'home_team': event['competitions'][0]['competitors'][0]['team']['abbreviation'],
                        'away_team': event['competitions'][0]['competitors'][1]['team']['abbreviation'],
                        'home_score': int(event['competitions'][0]['competitors'][0].get('score', 0)),
                        'away_score': int(event['competitions'][0]['competitors'][1].get('score', 0)),
                        'last_updated': datetime.now().isoformat()
                    }
                    live_games[game_id] = game_info
            
            logger.info(f"Found {len(live_games)} live/completed games")
            return live_games
            
        except Exception as e:
            logger.error(f"Error getting live games: {e}")
            return {}
    
    async def get_player_stats(self, game_id: str) -> Dict:
        """Get live player stats for a specific game"""
        try:
            url = f"{self.espn_base}/summary?event={game_id}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            player_stats = {}
            
            # Parse boxscore data
            if 'boxscore' in data:
                for team in data['boxscore']['teams']:
                    team_abbr = team['team']['abbreviation']
                    
                    # Parse statistics for each player
                    for stat_category in team.get('statistics', []):
                        category_name = stat_category.get('name', '').lower()
                        
                        for athlete in stat_category.get('athletes', []):
                            player_name = athlete['athlete']['displayName']
                            player_id = athlete['athlete']['id']
                            
                            if player_name not in player_stats:
                                player_stats[player_name] = {
                                    'player_id': player_id,
                                    'team': team_abbr,
                                    'stats': {}
                                }
                            
                            # Parse individual stats
                            for stat in athlete.get('stats', []):
                                stat_name = stat.get('name', '').lower().replace(' ', '_')
                                stat_value = float(stat.get('value', 0))
                                player_stats[player_name]['stats'][stat_name] = stat_value
            
            return player_stats
            
        except Exception as e:
            logger.error(f"Error getting player stats for game {game_id}: {e}")
            return {}
    
    def calculate_dfs_points(self, player_name: str, position: str, stats: Dict) -> float:
        """Calculate DFS points for a player based on their stats"""
        if position not in self.scoring:
            return 0.0
        
        total_points = 0.0
        position_scoring = self.scoring[position]
        
        for stat_name, multiplier in position_scoring.items():
            stat_value = stats.get(stat_name, 0)
            points = stat_value * multiplier
            total_points += points
        
        # Special handling for DST points allowed
        if position == 'DST' and 'points_allowed' in stats:
            points_allowed = stats['points_allowed']
            if points_allowed == 0:
                total_points += 10
            elif points_allowed <= 6:
                total_points += 7
            elif points_allowed <= 13:
                total_points += 4
            elif points_allowed <= 20:
                total_points += 1
            elif points_allowed <= 27:
                total_points += 0
            elif points_allowed <= 34:
                total_points -= 1
            else:
                total_points -= 4
        
        return round(total_points, 2)
    
    def register_lineup(self, lineup_id: str, lineup: Dict) -> bool:
        """Register a lineup for live tracking"""
        try:
            self.active_lineups[lineup_id] = {
                'lineup': lineup,
                'registered_at': datetime.now().isoformat(),
                'live_score': 0.0,
                'player_scores': {},
                'last_updated': None
            }
            logger.info(f"Registered lineup {lineup_id} for live tracking")
            return True
        except Exception as e:
            logger.error(f"Error registering lineup: {e}")
            return False
    
    async def update_lineup_scores(self) -> Dict:
        """Update scores for all registered lineups"""
        if not self.active_lineups:
            return {}
        
        # Get live games
        live_games = await self.get_live_games()
        
        # Update each lineup
        for lineup_id, lineup_data in self.active_lineups.items():
            total_score = 0.0
            player_scores = {}
            
            for player in lineup_data['lineup']['players']:
                player_name = player['name']
                position = player['position']
                
                # Find the player's game and get stats
                player_points = 0.0
                
                # For now, simulate live scoring with some realistic progression
                # In production, this would pull from actual game APIs
                if player_name not in lineup_data['player_scores']:
                    # Initialize with some base points
                    base_points = np.random.normal(player['projected_points'] * 0.3, 2)
                    player_points = max(0, base_points)
                else:
                    # Add incremental points as game progresses
                    current_points = lineup_data['player_scores'][player_name]
                    increment = np.random.normal(0.5, 1.0)
                    player_points = max(current_points, current_points + increment)
                
                player_scores[player_name] = round(player_points, 2)
                total_score += player_points
            
            # Update lineup data
            self.active_lineups[lineup_id]['live_score'] = round(total_score, 2)
            self.active_lineups[lineup_id]['player_scores'] = player_scores
            self.active_lineups[lineup_id]['last_updated'] = datetime.now().isoformat()
        
        return self.active_lineups
    
    def get_lineup_status(self, lineup_id: str) -> Optional[Dict]:
        """Get current status of a specific lineup"""
        return self.active_lineups.get(lineup_id)
    
    def get_all_lineups_status(self) -> Dict:
        """Get status of all registered lineups"""
        return self.active_lineups
    
    def simulate_game_progression(self, lineup_id: str, minutes_elapsed: int = 60) -> Dict:
        """Simulate how a lineup would score over time (for demo purposes)"""
        if lineup_id not in self.active_lineups:
            return {}
        
        lineup_data = self.active_lineups[lineup_id]
        progression = []
        
        # Simulate scoring every 5 minutes
        for minute in range(0, minutes_elapsed + 1, 5):
            total_score = 0.0
            player_scores = {}
            
            for player in lineup_data['lineup']['players']:
                player_name = player['name']
                projected = player['projected_points']
                
                # Simulate realistic scoring progression
                if minute == 0:
                    score = 0
                else:
                    # Players score more in certain periods
                    progress_pct = minute / 240.0  # 4 hours total game time
                    
                    # Add some randomness and position-specific patterns
                    if player['position'] == 'QB':
                        score = projected * progress_pct * np.random.uniform(0.8, 1.2)
                    elif player['position'] in ['RB', 'WR', 'TE']:
                        # Skill positions can have big plays
                        base_score = projected * progress_pct * np.random.uniform(0.7, 1.1)
                        if np.random.random() < 0.1:  # 10% chance of big play
                            base_score += np.random.uniform(6, 12)
                        score = base_score
                    elif player['position'] == 'K':
                        # Kickers score sporadically
                        score = projected * progress_pct * np.random.uniform(0.5, 1.5)
                    else:  # DST
                        score = projected * progress_pct * np.random.uniform(0.6, 1.4)
                
                player_scores[player_name] = round(max(0, score), 2)
                total_score += player_scores[player_name]
            
            progression.append({
                'minute': minute,
                'total_score': round(total_score, 2),
                'player_scores': player_scores.copy()
            })
        
        return {
            'lineup_id': lineup_id,
            'progression': progression,
            'final_score': progression[-1]['total_score'] if progression else 0
        }


# Mock data for testing
def create_mock_live_data():
    """Create mock live NFL data for testing"""
    
    mock_games = {
        'game_1': {
            'game_id': 'game_1',
            'status': 'STATUS_IN_PROGRESS',
            'clock': '8:42',
            'period': 2,
            'home_team': 'BUF',
            'away_team': 'MIA',
            'home_score': 14,
            'away_score': 10,
            'last_updated': datetime.now().isoformat()
        },
        'game_2': {
            'game_id': 'game_2', 
            'status': 'STATUS_IN_PROGRESS',
            'clock': '12:15',
            'period': 1,
            'home_team': 'DET',
            'away_team': 'ARI',
            'home_score': 7,
            'away_score': 3,
            'last_updated': datetime.now().isoformat()
        }
    }
    
    mock_player_stats = {
        'Josh Allen': {
            'team': 'BUF',
            'stats': {
                'passing_yards': 187,
                'passing_tds': 2,
                'interceptions': 0,
                'rushing_yards': 23,
                'rushing_tds': 0
            }
        },
        'Tyreek Hill': {
            'team': 'MIA',
            'stats': {
                'receptions': 4,
                'receiving_yards': 67,
                'receiving_tds': 1,
                'fumbles_lost': 0
            }
        }
    }
    
    return mock_games, mock_player_stats


# Example usage
if __name__ == "__main__":
    async def test_live_tracker():
        tracker = LiveNFLTracker()
        
        # Create a sample lineup
        sample_lineup = {
            'players': [
                {'name': 'Josh Allen', 'position': 'QB', 'projected_points': 24.2},
                {'name': 'Christian McCaffrey', 'position': 'RB', 'projected_points': 22.1},
                {'name': 'Tyreek Hill', 'position': 'WR', 'projected_points': 18.9},
            ]
        }
        
        # Register lineup
        tracker.register_lineup('test_lineup_1', sample_lineup)
        
        # Simulate game progression
        progression = tracker.simulate_game_progression('test_lineup_1', 120)
        
        print("Live Lineup Tracking Demo:")
        print(f"Final Score: {progression['final_score']}")
        
        # Show progression every 30 minutes
        for point in progression['progression'][::6]:  # Every 6th point (30 min intervals)
            print(f"Minute {point['minute']}: {point['total_score']} points")
    
    asyncio.run(test_live_tracker())
