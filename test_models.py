"""
Test script for NFL DFS AI Models and Lineup Optimizer
"""
import pandas as pd
import numpy as np
import sys
import os

# Add src to path
sys.path.append('src')

from models.player_performance_model import PlayerPerformanceModel
from models.ownership_model import OwnershipModel
from optimization.lineup_optimizer import LineupOptimizer

def create_sample_data():
    """Create realistic sample NFL DFS data"""
    np.random.seed(42)
    
    # NFL teams and positions
    teams = ['KC', 'BUF', 'SF', 'DAL', 'GB', 'NE', 'PIT', 'SEA', 'BAL', 'LAR']
    positions = ['QB', 'RB', 'WR', 'TE', 'K', 'DST']
    
    players = []
    player_id = 1
    
    for team in teams:
        for pos in positions:
            # Number of players per position
            if pos == 'QB':
                count = 1
                salary_range = (7000, 9000)
                points_mean = 18
            elif pos == 'RB':
                count = 3
                salary_range = (4500, 8500)
                points_mean = 12
            elif pos == 'WR':
                count = 4
                salary_range = (4000, 8000)
                points_mean = 11
            elif pos == 'TE':
                count = 2
                salary_range = (3500, 7000)
                points_mean = 8
            elif pos == 'K':
                count = 1
                salary_range = (4000, 5500)
                points_mean = 7
            else:  # DST
                count = 1
                salary_range = (2500, 4000)
                points_mean = 6
            
            for i in range(count):
                salary = np.random.randint(salary_range[0], salary_range[1])
                projected_points = max(0, np.random.normal(points_mean, points_mean * 0.3))
                
                players.append({
                    'player_id': f'player_{player_id}',
                    'name': f'{pos}{i+1}_{team}',
                    'position': pos,
                    'team': team,
                    'salary': salary,
                    'projected_points': projected_points,
                    'projected_ownership': max(0.5, np.random.exponential(8)),
                    'ceiling': projected_points * np.random.uniform(1.2, 1.5),
                    'floor': projected_points * np.random.uniform(0.5, 0.8),
                    'total_points': np.random.normal(45, 5),  # Game total
                    'spread': np.random.normal(0, 3),
                    'game_date': pd.Timestamp('2024-09-22'),
                    'week': 3
                })
                player_id += 1
    
    return pd.DataFrame(players)

def test_performance_model():
    """Test player performance prediction model"""
    print("🧠 Testing Player Performance Model...")
    
    # Create historical data for training
    historical_data = []
    for week in range(1, 10):  # 9 weeks of data
        weekly_data = create_sample_data()
        weekly_data['week'] = week
        weekly_data['game_date'] = pd.Timestamp('2024-09-01') + pd.Timedelta(weeks=week-1)
        
        # Add some historical stats
        weekly_data['fantasy_points'] = weekly_data['projected_points'] + np.random.normal(0, 2, len(weekly_data))
        weekly_data['targets'] = np.where(weekly_data['position'].isin(['WR', 'TE']),
                                        np.random.poisson(5), 0)
        weekly_data['carries'] = np.where(weekly_data['position'] == 'RB',
                                        np.random.poisson(8), 0)
        weekly_data['snaps'] = np.random.normal(50, 15, len(weekly_data))
        weekly_data['home_team'] = weekly_data['team']  # Add missing column
        weekly_data['opponent'] = np.random.choice(['KC', 'BUF', 'SF'], len(weekly_data))  # Add opponent
        
        historical_data.append(weekly_data)
    
    historical_df = pd.concat(historical_data, ignore_index=True)
    
    # Train model (without LSTM due to TensorFlow issues)
    model = PlayerPerformanceModel(position='ALL')
    
    try:
        metrics = model.train_models(historical_df)
        print(f"✅ Model trained successfully!")
        print(f"   XGBoost R²: {metrics['xgboost']['r2']:.3f}")
        print(f"   XGBoost MAE: {metrics['xgboost']['mae']:.2f}")
        
        # Test predictions
        current_week_data = create_sample_data()
        predictions = model.predict(current_week_data.head(10))
        
        print(f"\n📊 Sample Predictions:")
        for pred in predictions[:5]:
            print(f"   {pred['player_name']}: {pred['predicted_points']:.1f} pts "
                  f"(Floor: {pred['floor']:.1f}, Ceiling: {pred['ceiling']:.1f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance model test failed: {e}")
        return False

def test_ownership_model():
    """Test ownership prediction model"""
    print("\n🎯 Testing Ownership Model...")
    
    # Create sample data with ownership
    sample_data = create_sample_data()
    sample_data['ownership_pct'] = sample_data['projected_ownership']
    sample_data['projected_ceiling'] = sample_data['ceiling']  # Add missing column
    sample_data['projected_floor'] = sample_data['floor']  # Add missing column
    
    try:
        model = OwnershipModel()
        metrics = model.train_ownership_models(sample_data)
        
        print(f"✅ Ownership model trained successfully!")
        print(f"   Regression MAE: {metrics['regression_mae']:.2f}%")
        
        # Test predictions
        predictions = model.predict_ownership(sample_data.head(10))
        
        print(f"\n📈 Sample Ownership Predictions:")
        for pred in predictions[:5]:
            print(f"   {pred['player_name']}: {pred['predicted_ownership']:.1f}% "
                  f"({pred['ownership_tier']}) - Leverage: {pred['leverage_score']:.2f}")
        
        # Test contrarian plays
        contrarian = model.identify_contrarian_plays(predictions, min_points=8)
        if contrarian:
            print(f"\n🎪 Top Contrarian Play:")
            play = contrarian[0]
            print(f"   {play['player_name']}: {play['predicted_ownership']:.1f}% ownership")
        
        return True
        
    except Exception as e:
        print(f"❌ Ownership model test failed: {e}")
        return False

def test_lineup_optimizer():
    """Test lineup optimization engine"""
    print("\n⚡ Testing Lineup Optimizer...")
    
    try:
        # Create player pool
        players_df = create_sample_data()
        
        optimizer = LineupOptimizer()
        
        # Test single lineup generation
        lineup = optimizer.create_base_lineup(players_df, strategy='balanced')
        
        if lineup:
            print(f"✅ Single lineup generated successfully!")
            print(f"   Total Salary: ${lineup['total_salary']:,}")
            print(f"   Projected Points: {lineup['projected_points']:.1f}")
            print(f"   Salary Remaining: ${lineup['salary_remaining']}")
            
            print(f"\n📋 Lineup Composition:")
            for player in lineup['players']:
                print(f"   {player['position']}: {player['name']} - "
                      f"${player['salary']} ({player['projected_points']:.1f} pts)")
        
        # Test multiple lineup generation
        lineups = optimizer.generate_multiple_lineups(players_df, num_lineups=5)
        print(f"\n📊 Generated {len(lineups)} unique lineups")
        
        for i, lineup in enumerate(lineups):
            metrics = optimizer.calculate_lineup_metrics(lineup)
            print(f"   Lineup {i+1}: {metrics['projected_points']:.1f} pts, "
                  f"${metrics['total_salary']:,}, {metrics['teams_used']} teams")
        
        # Test constraints
        constraints = {
            'must_include': [players_df.iloc[0]['player_id']],
            'max_team_players': 3
        }
        
        constrained_lineup = optimizer.optimize_with_constraints(players_df, constraints)
        if constrained_lineup:
            print(f"\n🎯 Constrained lineup: {constrained_lineup['projected_points']:.1f} pts")
        
        return True
        
    except Exception as e:
        print(f"❌ Lineup optimizer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🏈 NFL DFS AI System - Model Testing")
    print("=" * 50)
    
    # Create directories
    os.makedirs('models', exist_ok=True)
    os.makedirs('lineups', exist_ok=True)
    
    results = []
    
    # Test each component
    results.append(("Performance Model", test_performance_model()))
    results.append(("Ownership Model", test_ownership_model()))
    results.append(("Lineup Optimizer", test_lineup_optimizer()))
    
    # Summary
    print(f"\n🏆 Test Results Summary:")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All systems operational! Your NFL DFS AI is ready!")
        print("\n🚀 Next steps:")
        print("   1. Integrate with real NFL data")
        print("   2. Set up database for historical tracking")
        print("   3. Build web dashboard")
        print("   4. Connect to DraftKings API")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
