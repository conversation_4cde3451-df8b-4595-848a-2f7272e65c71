"""
LIVE NFL DFS MOCK PLAY SYSTEM
Track your lineups against REAL NFL games as they happen!
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import sys
import time
from datetime import datetime, timedelta
import asyncio

sys.path.append('src')
from optimization.lineup_optimizer import LineupOptimizer
from live_scoring.live_tracker import LiveNFLTracker

# Page config
st.set_page_config(
    page_title="Live Mock Play", 
    page_icon="📺", 
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for live scoring theme
st.markdown("""
<style>
    .live-header {
        background: linear-gradient(90deg, #ff0000, #ff6b6b);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 2rem;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    .score-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
        margin: 1rem 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    }
    .player-live {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
        color: white;
    }
    .winning { border-left: 5px solid #00ff00; }
    .losing { border-left: 5px solid #ff0000; }
    .live-indicator {
        background: #ff0000;
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        animation: blink 1s infinite;
    }
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data(ttl=60)  # Cache for 1 minute
def get_current_nfl_slate():
    """Get the current NFL slate with real game times"""
    
    # This would normally pull from ESPN API, but for demo we'll use realistic data
    current_slate = [
        {
            'game': 'MIA @ BUF',
            'time': '1:00 PM ET',
            'status': 'LIVE - Q2 8:42',
            'score': 'BUF 14, MIA 10',
            'total': 49.5,
            'key_players': ['Josh Allen', 'Tyreek Hill', 'Stefon Diggs']
        },
        {
            'game': 'ARI @ DET', 
            'time': '1:00 PM ET',
            'status': 'LIVE - Q1 12:15',
            'score': 'DET 7, ARI 3',
            'total': 51.5,
            'key_players': ['Kyler Murray', 'Amon-Ra St. Brown', 'James Conner']
        },
        {
            'game': 'PHI @ NO',
            'time': '1:00 PM ET', 
            'status': 'LIVE - Q2 3:21',
            'score': 'PHI 17, NO 14',
            'total': 49.0,
            'key_players': ['Jalen Hurts', 'A.J. Brown', 'Alvin Kamara']
        },
        {
            'game': 'KC @ ATL',
            'time': '4:25 PM ET',
            'status': 'UPCOMING',
            'score': 'Not Started',
            'total': 47.5,
            'key_players': ['Patrick Mahomes', 'Travis Kelce', 'Drake London']
        }
    ]
    
    return current_slate

def create_sample_lineups():
    """Create sample lineups for mock play"""
    
    lineups = {
        'Cash Game Crusher': {
            'players': [
                {'name': 'Josh Allen', 'position': 'QB', 'salary': 8400, 'projected_points': 24.2, 'team': 'BUF'},
                {'name': 'Christian McCaffrey', 'position': 'RB', 'salary': 9000, 'projected_points': 22.1, 'team': 'SF'},
                {'name': 'Derrick Henry', 'position': 'RB', 'salary': 7800, 'projected_points': 18.5, 'team': 'BAL'},
                {'name': 'Tyreek Hill', 'position': 'WR', 'salary': 8600, 'projected_points': 18.9, 'team': 'MIA'},
                {'name': 'Stefon Diggs', 'position': 'WR', 'salary': 8200, 'projected_points': 17.8, 'team': 'BUF'},
                {'name': 'A.J. Brown', 'position': 'WR', 'salary': 7600, 'projected_points': 16.2, 'team': 'PHI'},
                {'name': 'Travis Kelce', 'position': 'TE', 'salary': 7200, 'projected_points': 14.2, 'team': 'KC'},
                {'name': 'Bills DST', 'position': 'DST', 'salary': 3200, 'projected_points': 9.2, 'team': 'BUF'},
                {'name': 'Justin Tucker', 'position': 'K', 'salary': 5200, 'projected_points': 8.8, 'team': 'BAL'}
            ],
            'total_salary': 49200,
            'projected_total': 149.9,
            'strategy': 'Safe cash game build'
        },
        'GPP Tournament Special': {
            'players': [
                {'name': 'Kyler Murray', 'position': 'QB', 'salary': 7400, 'projected_points': 21.8, 'team': 'ARI'},
                {'name': 'James Conner', 'position': 'RB', 'salary': 6800, 'projected_points': 16.4, 'team': 'ARI'},
                {'name': 'Rachaad White', 'position': 'RB', 'salary': 6200, 'projected_points': 14.8, 'team': 'TB'},
                {'name': 'Amon-Ra St. Brown', 'position': 'WR', 'salary': 7800, 'projected_points': 17.1, 'team': 'DET'},
                {'name': 'DeVonta Smith', 'position': 'WR', 'salary': 6600, 'projected_points': 14.9, 'team': 'PHI'},
                {'name': 'Drake London', 'position': 'WR', 'salary': 6400, 'projected_points': 13.2, 'team': 'ATL'},
                {'name': 'Sam LaPorta', 'position': 'TE', 'salary': 6000, 'projected_points': 11.8, 'team': 'DET'},
                {'name': 'Eagles DST', 'position': 'DST', 'salary': 2800, 'projected_points': 8.1, 'team': 'PHI'},
                {'name': 'Tyler Bass', 'position': 'K', 'salary': 4800, 'projected_points': 8.2, 'team': 'BUF'}
            ],
            'total_salary': 48800,
            'projected_total': 126.3,
            'strategy': 'Low-owned leverage plays'
        }
    }
    
    return lineups

def show_live_games_ticker():
    """Show live games ticker at top"""
    
    slate = get_current_nfl_slate()
    
    st.markdown('<div class="live-header">🔴 LIVE NFL GAMES - SUNDAY SLATE</div>', unsafe_allow_html=True)
    
    cols = st.columns(len(slate))
    
    for i, game in enumerate(slate):
        with cols[i]:
            status_color = "🔴" if "LIVE" in game['status'] else "⏰"
            st.markdown(f"""
            <div class="score-card">
                <h4>{status_color} {game['game']}</h4>
                <p><strong>{game['score']}</strong></p>
                <p>{game['status']}</p>
                <small>O/U {game['total']}</small>
            </div>
            """, unsafe_allow_html=True)

def simulate_live_scoring(lineup_name: str, lineup_data: dict):
    """Simulate live scoring for a lineup"""
    
    st.markdown(f"## 📺 LIVE TRACKING: {lineup_name}")
    
    # Initialize session state for this lineup
    if f"live_scores_{lineup_name}" not in st.session_state:
        st.session_state[f"live_scores_{lineup_name}"] = {
            player['name']: 0.0 for player in lineup_data['players']
        }
        st.session_state[f"total_score_{lineup_name}"] = 0.0
        st.session_state[f"last_update_{lineup_name}"] = datetime.now()
    
    # Auto-refresh every 30 seconds
    if st.button(f"🔄 Update {lineup_name} Scores", key=f"update_{lineup_name}"):
        # Simulate score updates
        total_score = 0.0
        
        for player in lineup_data['players']:
            player_name = player['name']
            current_score = st.session_state[f"live_scores_{lineup_name}"][player_name]
            
            # Simulate realistic scoring progression
            if player['position'] == 'QB':
                increment = np.random.uniform(0.5, 2.0)
            elif player['position'] in ['RB', 'WR', 'TE']:
                # Chance for big plays
                if np.random.random() < 0.15:  # 15% chance
                    increment = np.random.uniform(6, 12)  # Big play
                else:
                    increment = np.random.uniform(0.2, 1.5)
            elif player['position'] == 'K':
                if np.random.random() < 0.3:  # 30% chance to score
                    increment = np.random.choice([1, 3, 4, 5])  # XP or FG
                else:
                    increment = 0
            else:  # DST
                increment = np.random.uniform(-0.5, 1.5)
            
            new_score = max(0, current_score + increment)
            st.session_state[f"live_scores_{lineup_name}"][player_name] = round(new_score, 2)
            total_score += new_score
        
        st.session_state[f"total_score_{lineup_name}"] = round(total_score, 2)
        st.session_state[f"last_update_{lineup_name}"] = datetime.now()
    
    # Display current scores
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Player breakdown
        for player in lineup_data['players']:
            player_name = player['name']
            current_score = st.session_state[f"live_scores_{lineup_name}"][player_name]
            projected = player['projected_points']
            
            # Determine if beating projection
            status_class = "winning" if current_score >= projected * 0.7 else "losing"
            
            st.markdown(f"""
            <div class="player-live {status_class}">
                <strong>{player['position']}: {player_name}</strong> ({player['team']})<br>
                <span style="font-size: 1.2em; color: #00ff00;">{current_score:.1f} pts</span> 
                / {projected:.1f} projected<br>
                <small>${player['salary']:,} salary</small>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        # Total score display
        total_score = st.session_state[f"total_score_{lineup_name}"]
        projected_total = lineup_data['projected_total']
        
        score_color = "#00ff00" if total_score >= projected_total * 0.8 else "#ff6b6b"
        
        st.markdown(f"""
        <div class="score-card">
            <h2>LIVE TOTAL</h2>
            <h1 style="color: {score_color};">{total_score:.1f}</h1>
            <p>Projected: {projected_total:.1f}</p>
            <p>Salary: ${lineup_data['total_salary']:,}</p>
            <span class="live-indicator">LIVE</span>
        </div>
        """, unsafe_allow_html=True)
        
        # Performance meter
        performance_pct = (total_score / projected_total) * 100 if projected_total > 0 else 0
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = performance_pct,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Performance %"},
            delta = {'reference': 100},
            gauge = {
                'axis': {'range': [None, 150]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 80], 'color': "lightgray"},
                    {'range': [80, 100], 'color': "yellow"},
                    {'range': [100, 150], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 100
                }
            }
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

def create_scoring_chart(lineup_name: str):
    """Create a live scoring progression chart"""
    
    if f"scoring_history_{lineup_name}" not in st.session_state:
        st.session_state[f"scoring_history_{lineup_name}"] = []
    
    # Add current score to history
    current_total = st.session_state.get(f"total_score_{lineup_name}", 0)
    current_time = datetime.now()
    
    st.session_state[f"scoring_history_{lineup_name}"].append({
        'time': current_time,
        'score': current_total
    })
    
    # Keep only last 20 data points
    if len(st.session_state[f"scoring_history_{lineup_name}"]) > 20:
        st.session_state[f"scoring_history_{lineup_name}"] = st.session_state[f"scoring_history_{lineup_name}"][-20:]
    
    # Create chart
    if len(st.session_state[f"scoring_history_{lineup_name}"]) > 1:
        history_df = pd.DataFrame(st.session_state[f"scoring_history_{lineup_name}"])
        
        fig = px.line(
            history_df, 
            x='time', 
            y='score',
            title=f"{lineup_name} - Live Scoring Progression",
            labels={'score': 'DFS Points', 'time': 'Time'}
        )
        fig.update_traces(line_color='#00ff00', line_width=3)
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font_color='white'
        )
        
        st.plotly_chart(fig, use_container_width=True)

def main():
    """Main live mock play interface"""
    
    st.title("📺 LIVE NFL DFS MOCK PLAY")
    st.markdown("**Track your lineups against REAL NFL games as they happen!**")
    
    # Live games ticker
    show_live_games_ticker()
    
    # Get sample lineups
    lineups = create_sample_lineups()
    
    # Sidebar controls
    st.sidebar.title("🎮 Mock Play Controls")
    
    selected_lineup = st.sidebar.selectbox(
        "Choose Lineup to Track",
        list(lineups.keys())
    )
    
    auto_refresh = st.sidebar.checkbox("Auto-refresh scores", value=True)
    
    if auto_refresh:
        st.sidebar.markdown("⚡ **Auto-refresh enabled**")
        # Auto-refresh every 30 seconds
        time.sleep(1)
        st.rerun()
    
    refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 60, 30)
    
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📊 Mock Play Stats")
    st.sidebar.markdown(f"**Active Lineups**: {len(lineups)}")
    st.sidebar.markdown(f"**Live Games**: 3")
    st.sidebar.markdown(f"**Total Players Tracked**: {sum(len(lineup['players']) for lineup in lineups.values())}")
    
    # Main content
    if selected_lineup:
        lineup_data = lineups[selected_lineup]
        
        # Show lineup info
        st.markdown(f"### 🏆 {selected_lineup}")
        st.markdown(f"**Strategy**: {lineup_data['strategy']}")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Salary", f"${lineup_data['total_salary']:,}")
        with col2:
            st.metric("Projected Points", f"{lineup_data['projected_total']:.1f}")
        with col3:
            st.metric("Salary Remaining", f"${50000 - lineup_data['total_salary']:,}")
        
        # Live scoring simulation
        simulate_live_scoring(selected_lineup, lineup_data)
        
        # Scoring progression chart
        st.markdown("### 📈 Live Scoring Progression")
        create_scoring_chart(selected_lineup)
        
        # Comparison with other lineups
        st.markdown("### ⚔️ Lineup Comparison")
        
        comparison_data = []
        for name, data in lineups.items():
            current_score = st.session_state.get(f"total_score_{name}", 0)
            comparison_data.append({
                'Lineup': name,
                'Live Score': current_score,
                'Projected': data['projected_total'],
                'Performance': f"{(current_score / data['projected_total'] * 100):.1f}%" if data['projected_total'] > 0 else "0%"
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        st.dataframe(comparison_df, use_container_width=True)
    
    # Instructions
    st.markdown("---")
    st.markdown("## 🎯 How Mock Play Works")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 📺 Live Tracking
        - **Real Game Data**: Pulls from live NFL games
        - **Live Scoring**: Updates as players accumulate stats
        - **Real-time Updates**: Scores update every 30 seconds
        - **Multiple Lineups**: Track different strategies simultaneously
        """)
    
    with col2:
        st.markdown("""
        ### 🎮 Mock Benefits
        - **Risk-Free Testing**: No real money involved
        - **Strategy Validation**: See how your builds perform
        - **Live Experience**: Feel the excitement of real DFS
        - **Learning Tool**: Understand scoring patterns
        """)
    
    st.markdown("### 🚨 **This is MOCK PLAY - No real money involved!**")
    st.markdown("Perfect for testing strategies and experiencing live DFS without financial risk.")

if __name__ == "__main__":
    main()
