"""
Configuration settings for NFL DFS AI System
"""
import os
from dotenv import load_dotenv

load_dotenv()

# API Keys (set these in your .env file)
ESPN_API_KEY = os.getenv('ESPN_API_KEY', '')
WEATHER_API_KEY = os.getenv('WEATHER_API_KEY', '')
ODDS_API_KEY = os.getenv('ODDS_API_KEY', '')
DRAFTKINGS_API_KEY = os.getenv('DRAFTKINGS_API_KEY', '')
FANTASYDATA_API_KEY = os.getenv('FANTASYDATA_API_KEY', '')

# Database Configuration
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///nfl_dfs.db')

# DFS Platform Settings
PLATFORM = 'draftkings'
SPORT = 'nfl'
SALARY_CAP = 50000

# Model Settings
MODEL_RETRAIN_DAYS = 7
PREDICTION_CONFIDENCE_THRESHOLD = 0.7

# Lineup Generation Settings
MAX_LINEUPS = 150
MAX_PLAYER_EXPOSURE = 0.3
MIN_PLAYER_EXPOSURE = 0.05

# Data Collection Settings
DATA_UPDATE_INTERVAL_MINUTES = 15
INJURY_CHECK_INTERVAL_MINUTES = 5

# API Endpoints
ESPN_BASE_URL = "https://site.api.espn.com/apis/site/v2/sports/football/nfl"
NFL_BASE_URL = "https://api.nfl.com/v1"
WEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5"
ODDS_BASE_URL = "https://api.the-odds-api.com/v4"

# File Paths
DATA_DIR = "data"
MODELS_DIR = "models"
LOGS_DIR = "logs"
LINEUPS_DIR = "lineups"

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
