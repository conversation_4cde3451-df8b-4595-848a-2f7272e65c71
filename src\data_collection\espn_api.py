"""
ESPN API client for NFL data collection
Provides player stats, schedules, injuries, and team information
"""
import requests
import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import asyncio
import aiohttp
from loguru import logger

from config import ESPN_BASE_URL


class ESPNClient:
    """Client for ESPN NFL API"""
    
    def __init__(self):
        self.base_url = ESPN_BASE_URL
        self.session = requests.Session()
        
    async def get_current_week(self) -> int:
        """Get current NFL week"""
        try:
            url = f"{self.base_url}/scoreboard"
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            return data.get('week', {}).get('number', 1)
        except Exception as e:
            logger.error(f"Error getting current week: {e}")
            return 1
    
    async def get_teams(self) -> pd.DataFrame:
        """Get all NFL teams"""
        try:
            url = f"{self.base_url}/teams"
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            teams = []
            for team in data.get('sports', [{}])[0].get('leagues', [{}])[0].get('teams', []):
                teams.append({
                    'team_id': team['team']['id'],
                    'abbreviation': team['team']['abbreviation'],
                    'display_name': team['team']['displayName'],
                    'name': team['team']['name'],
                    'location': team['team']['location'],
                    'color': team['team'].get('color', ''),
                    'logo': team['team'].get('logos', [{}])[0].get('href', '')
                })
            
            return pd.DataFrame(teams)
        except Exception as e:
            logger.error(f"Error getting teams: {e}")
            return pd.DataFrame()
    
    async def get_schedule(self, week: Optional[int] = None, season: Optional[int] = None) -> pd.DataFrame:
        """Get NFL schedule for specific week/season"""
        try:
            current_year = datetime.now().year
            season = season or current_year
            
            if week:
                url = f"{self.base_url}/scoreboard?seasontype=2&week={week}&year={season}"
            else:
                url = f"{self.base_url}/scoreboard?seasontype=2&year={season}"
            
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            games = []
            for event in data.get('events', []):
                game_info = {
                    'game_id': event['id'],
                    'date': event['date'],
                    'week': data.get('week', {}).get('number'),
                    'season': season,
                    'status': event['status']['type']['name'],
                    'home_team_id': event['competitions'][0]['competitors'][0]['team']['id'],
                    'away_team_id': event['competitions'][0]['competitors'][1]['team']['id'],
                    'home_team': event['competitions'][0]['competitors'][0]['team']['abbreviation'],
                    'away_team': event['competitions'][0]['competitors'][1]['team']['abbreviation'],
                    'home_score': event['competitions'][0]['competitors'][0].get('score', 0),
                    'away_score': event['competitions'][0]['competitors'][1].get('score', 0),
                    'venue': event['competitions'][0]['venue']['fullName'],
                    'city': event['competitions'][0]['venue']['address']['city'],
                    'state': event['competitions'][0]['venue']['address']['state']
                }
                games.append(game_info)
            
            return pd.DataFrame(games)
        except Exception as e:
            logger.error(f"Error getting schedule: {e}")
            return pd.DataFrame()
    
    async def get_player_stats(self, season: Optional[int] = None) -> pd.DataFrame:
        """Get player statistics for the season"""
        try:
            current_year = datetime.now().year
            season = season or current_year
            
            # Get team rosters and stats
            teams_df = await self.get_teams()
            all_players = []
            
            for _, team in teams_df.iterrows():
                team_id = team['team_id']
                url = f"{self.base_url}/teams/{team_id}/roster"
                
                response = self.session.get(url)
                if response.status_code == 200:
                    data = response.json()
                    
                    for athlete in data.get('athletes', []):
                        player_info = {
                            'player_id': athlete.get('id', ''),
                            'name': athlete.get('fullName', athlete.get('displayName', '')),
                            'position': athlete.get('position', {}).get('abbreviation', ''),
                            'team_id': team_id,
                            'team': team['abbreviation'],
                            'jersey': athlete.get('jersey', ''),
                            'age': athlete.get('age', 0),
                            'height': athlete.get('displayHeight', ''),
                            'weight': athlete.get('weight', 0),
                            'experience': athlete.get('experience', {}).get('years', 0)
                        }
                        all_players.append(player_info)
                
                # Rate limiting
                await asyncio.sleep(0.1)
            
            return pd.DataFrame(all_players)
        except Exception as e:
            logger.error(f"Error getting player stats: {e}")
            return pd.DataFrame()
    
    async def get_injuries(self) -> pd.DataFrame:
        """Get current injury report"""
        try:
            # ESPN doesn't have a direct injury endpoint, but we can get it from team pages
            teams_df = await self.get_teams()
            all_injuries = []
            
            for _, team in teams_df.iterrows():
                team_id = team['team_id']
                url = f"{self.base_url}/teams/{team_id}"
                
                response = self.session.get(url)
                if response.status_code == 200:
                    # This would need to be parsed from the team page
                    # For now, return empty DataFrame
                    pass
                
                await asyncio.sleep(0.1)
            
            return pd.DataFrame(all_injuries)
        except Exception as e:
            logger.error(f"Error getting injuries: {e}")
            return pd.DataFrame()


# Example usage
async def main():
    client = ESPNClient()
    
    # Get current week
    week = await client.get_current_week()
    print(f"Current week: {week}")
    
    # Get teams
    teams = await client.get_teams()
    print(f"Found {len(teams)} teams")
    
    # Get schedule
    schedule = await client.get_schedule(week=week)
    print(f"Found {len(schedule)} games this week")
    
    # Get players
    players = await client.get_player_stats()
    print(f"Found {len(players)} players")


if __name__ == "__main__":
    asyncio.run(main())
