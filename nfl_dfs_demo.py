"""
NFL DFS AI System - Complete Demo
Shows the full pipeline from data to optimized lineups
"""
import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append('src')

from optimization.lineup_optimizer import LineupOptimizer

def create_realistic_player_pool():
    """Create a realistic NFL DFS player pool for Week 3"""
    np.random.seed(42)
    
    # Real NFL teams and realistic data
    teams_data = {
        'KC': {'total': 48.5, 'spread': -3.5, 'pace': 'High'},
        'BUF': {'total': 47.0, 'spread': -7.0, 'pace': 'High'},
        'SF': {'total': 45.5, 'spread': -4.0, 'pace': 'Medium'},
        'DAL': {'total': 46.0, 'spread': -2.5, 'pace': 'High'},
        'GB': {'total': 44.5, 'spread': -1.0, 'pace': 'Medium'},
        'BAL': {'total': 48.0, 'spread': 2.5, 'pace': 'High'},
        'MIA': {'total': 43.0, 'spread': 6.5, 'pace': 'Medium'},
        'NYJ': {'total': 43.0, 'spread': -6.5, 'pace': 'Low'}
    }
    
    players = []
    player_id = 1
    
    for team, team_info in teams_data.items():
        total = team_info['total']
        spread = team_info['spread']
        pace_multiplier = {'High': 1.1, 'Medium': 1.0, 'Low': 0.9}[team_info['pace']]
        
        # QB - 1 per team
        qb_salary = np.random.randint(7200, 8800)
        qb_points = (18 + (total - 45) * 0.3 + abs(spread) * 0.1) * pace_multiplier
        qb_ownership = np.random.uniform(8, 25)
        
        players.append({
            'player_id': f'qb_{team.lower()}',
            'name': f'QB_{team}',
            'position': 'QB',
            'team': team,
            'salary': qb_salary,
            'projected_points': max(12, qb_points + np.random.normal(0, 2)),
            'projected_ownership': qb_ownership,
            'ceiling': qb_points * 1.4,
            'floor': qb_points * 0.6
        })
        
        # RB - 2-3 per team
        for i in range(3):
            rb_salary = np.random.randint(4000, 8000) if i == 0 else np.random.randint(4000, 6500)
            rb_points = (12 - i * 3) * pace_multiplier
            rb_ownership = np.random.uniform(3, 20) if i == 0 else np.random.uniform(1, 8)
            
            players.append({
                'player_id': f'rb{i+1}_{team.lower()}',
                'name': f'RB{i+1}_{team}',
                'position': 'RB',
                'team': team,
                'salary': rb_salary,
                'projected_points': max(4, rb_points + np.random.normal(0, 2)),
                'projected_ownership': rb_ownership,
                'ceiling': rb_points * 1.5,
                'floor': rb_points * 0.5
            })
        
        # WR - 3-4 per team
        for i in range(4):
            wr_salary = np.random.randint(4500, 7500) if i < 2 else np.random.randint(4000, 6000)
            wr_points = (11 - i * 2) * pace_multiplier * (1 + (total - 45) * 0.02)
            wr_ownership = np.random.uniform(5, 18) if i < 2 else np.random.uniform(1, 8)
            
            players.append({
                'player_id': f'wr{i+1}_{team.lower()}',
                'name': f'WR{i+1}_{team}',
                'position': 'WR',
                'team': team,
                'salary': wr_salary,
                'projected_points': max(4, wr_points + np.random.normal(0, 1.5)),
                'projected_ownership': wr_ownership,
                'ceiling': wr_points * 1.6,
                'floor': wr_points * 0.4
            })
        
        # TE - 2 per team
        for i in range(2):
            te_salary = np.random.randint(3500, 6500) if i == 0 else np.random.randint(3000, 4500)
            te_points = (8 - i * 3) * pace_multiplier
            te_ownership = np.random.uniform(3, 15) if i == 0 else np.random.uniform(1, 5)
            
            players.append({
                'player_id': f'te{i+1}_{team.lower()}',
                'name': f'TE{i+1}_{team}',
                'position': 'TE',
                'team': team,
                'salary': te_salary,
                'projected_points': max(2, te_points + np.random.normal(0, 1)),
                'projected_ownership': te_ownership,
                'ceiling': te_points * 1.4,
                'floor': te_points * 0.6
            })
        
        # K - 1 per team
        k_salary = np.random.randint(4000, 5200)
        k_points = 7 + (total - 45) * 0.1
        k_ownership = np.random.uniform(2, 12)
        
        players.append({
            'player_id': f'k_{team.lower()}',
            'name': f'K_{team}',
            'position': 'K',
            'team': team,
            'salary': k_salary,
            'projected_points': max(4, k_points + np.random.normal(0, 1)),
            'projected_ownership': k_ownership,
            'ceiling': k_points * 1.3,
            'floor': k_points * 0.7
        })
        
        # DST - 1 per team
        dst_salary = np.random.randint(2200, 3800)
        dst_points = 6 + max(0, -spread * 0.2)  # Better if favored
        dst_ownership = np.random.uniform(2, 15)
        
        players.append({
            'player_id': f'dst_{team.lower()}',
            'name': f'DST_{team}',
            'position': 'DST',
            'team': team,
            'salary': dst_salary,
            'projected_points': max(2, dst_points + np.random.normal(0, 2)),
            'projected_ownership': dst_ownership,
            'ceiling': dst_points * 1.8,
            'floor': dst_points * 0.2
        })
    
    return pd.DataFrame(players)

def analyze_player_pool(df):
    """Analyze the player pool for insights"""
    print("📊 Player Pool Analysis")
    print("=" * 30)
    
    # Position breakdown
    pos_stats = df.groupby('position').agg({
        'projected_points': ['count', 'mean', 'max'],
        'salary': ['mean', 'max'],
        'projected_ownership': 'mean'
    }).round(1)
    
    print("Position Breakdown:")
    for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'DST']:
        count = pos_stats.loc[pos, ('projected_points', 'count')]
        avg_pts = pos_stats.loc[pos, ('projected_points', 'mean')]
        max_pts = pos_stats.loc[pos, ('projected_points', 'max')]
        avg_sal = pos_stats.loc[pos, ('salary', 'mean')]
        avg_own = pos_stats.loc[pos, ('projected_ownership', 'mean')]
        
        print(f"  {pos}: {count} players, Avg: {avg_pts:.1f} pts (${avg_sal:,.0f}), "
              f"Max: {max_pts:.1f} pts, Own: {avg_own:.1f}%")
    
    # Top values by position
    print(f"\n💎 Top Values (Points per $1K):")
    df['value'] = df['projected_points'] / (df['salary'] / 1000)
    
    for pos in ['QB', 'RB', 'WR', 'TE']:
        top_player = df[df['position'] == pos].nlargest(1, 'value').iloc[0]
        print(f"  {pos}: {top_player['name']} - {top_player['projected_points']:.1f} pts, "
              f"${top_player['salary']}, Value: {top_player['value']:.2f}")

def generate_lineup_strategies(df, optimizer):
    """Generate lineups for different strategies"""
    strategies = {
        'cash': 'Safe, high-floor plays for cash games',
        'balanced': 'Balanced approach for mixed contests', 
        'gpp': 'High-ceiling plays for tournaments'
    }
    
    lineups = {}
    
    print(f"\n🎯 Generating Lineups by Strategy")
    print("=" * 40)
    
    for strategy, description in strategies.items():
        lineup = optimizer.create_base_lineup(df, strategy=strategy)
        if lineup:
            lineups[strategy] = lineup
            metrics = optimizer.calculate_lineup_metrics(lineup)
            
            print(f"\n{strategy.upper()} Strategy - {description}")
            print(f"  Projected Points: {metrics['projected_points']:.1f}")
            print(f"  Ceiling: {metrics['projected_ceiling']:.1f}")
            print(f"  Floor: {metrics['projected_floor']:.1f}")
            print(f"  Total Salary: ${metrics['total_salary']:,}")
            print(f"  Avg Ownership: {metrics['avg_ownership']:.1f}%")
            print(f"  Teams Used: {metrics['teams_used']}")
            
            print("  Lineup:")
            for player in lineup['players']:
                print(f"    {player['position']}: {player['name']} - "
                      f"${player['salary']} ({player['projected_points']:.1f} pts)")
    
    return lineups

def demonstrate_advanced_features(df, optimizer):
    """Show advanced optimization features"""
    print(f"\n🔧 Advanced Features Demo")
    print("=" * 30)
    
    # 1. Stacking Strategy
    print("1. QB-WR Stacking:")
    stacked_pool = optimizer.apply_stacking_strategy(df, stack_type='qb_wr')
    stack_lineup = optimizer.create_base_lineup(stacked_pool, strategy='gpp')
    
    if stack_lineup:
        # Check for stacks
        qb_team = None
        wr_teammates = []
        
        for player in stack_lineup['players']:
            if player['position'] == 'QB':
                qb_team = player['team']
            elif player['position'] == 'WR' and player['team'] == qb_team:
                wr_teammates.append(player['name'])
        
        if wr_teammates:
            print(f"  ✅ Stack found: QB_{qb_team} + {', '.join(wr_teammates)}")
        else:
            print(f"  No stack in this lineup")
    
    # 2. Constraints
    print(f"\n2. Custom Constraints:")
    
    # Must include a specific player
    top_qb = df[df['position'] == 'QB'].nlargest(1, 'projected_points').iloc[0]
    constraints = {
        'must_include': [top_qb['player_id']],
        'max_team_players': 3,
        'min_salary': 48000
    }
    
    constrained_lineup = optimizer.optimize_with_constraints(df, constraints)
    if constrained_lineup:
        print(f"  ✅ Forced {top_qb['name']} into lineup")
        print(f"  Total: {constrained_lineup['projected_points']:.1f} pts, "
              f"${constrained_lineup['total_salary']:,}")
    
    # 3. Multiple Lineups with Exposure Control
    print(f"\n3. Multiple Lineups (Exposure Control):")
    multiple_lineups = optimizer.generate_multiple_lineups(df, num_lineups=10)
    
    # Calculate exposure
    exposure_report = optimizer.get_exposure_report()
    if not exposure_report.empty:
        high_exposure = exposure_report.head(3)
        print(f"  Generated {len(multiple_lineups)} unique lineups")
        print(f"  Highest exposures:")
        for _, player in high_exposure.iterrows():
            player_name = df[df['player_id'] == player['player_id']]['name'].iloc[0]
            print(f"    {player_name}: {player['exposure_pct']:.1f}%")

def main():
    """Run the complete NFL DFS AI demo"""
    print("🏈 NFL DFS AI System - Complete Demo")
    print("=" * 50)
    print("Simulating Week 3 NFL DFS optimization...")
    
    # Create realistic player pool
    player_pool = create_realistic_player_pool()
    print(f"✅ Created player pool: {len(player_pool)} players across 8 teams")
    
    # Analyze player pool
    analyze_player_pool(player_pool)
    
    # Initialize optimizer
    optimizer = LineupOptimizer()
    
    # Generate different strategy lineups
    lineups = generate_lineup_strategies(player_pool, optimizer)
    
    # Demonstrate advanced features
    demonstrate_advanced_features(player_pool, optimizer)
    
    # Final summary
    print(f"\n🏆 System Performance Summary")
    print("=" * 35)
    print(f"✅ Player Pool: {len(player_pool)} players processed")
    print(f"✅ Strategies: {len(lineups)} different lineup strategies")
    print(f"✅ Optimization: Mathematical programming with constraints")
    print(f"✅ Features: Stacking, exposure control, custom constraints")
    
    print(f"\n🚀 Your NFL DFS AI System is fully operational!")
    print(f"\nNext steps to go live:")
    print(f"  1. Connect to real NFL data APIs ✅ (Already implemented)")
    print(f"  2. Add historical performance tracking")
    print(f"  3. Build web dashboard for lineup management")
    print(f"  4. Integrate with DraftKings for automated entry")
    print(f"  5. Add live injury/news monitoring")
    
    print(f"\n💡 Pro Tips:")
    print(f"  • Use 'cash' strategy for 50/50s and double-ups")
    print(f"  • Use 'gpp' strategy for large tournaments")
    print(f"  • Stack QB+WR in high-total games (47+ points)")
    print(f"  • Limit exposure to 20-30% in tournaments")
    print(f"  • Target players with <10% ownership for leverage")

if __name__ == "__main__":
    main()
