"""
NFL DFS Lineup Optimization Engine
Uses mathematical optimization to generate optimal lineups with constraints
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import pulp
from itertools import combinations
import random
from datetime import datetime
from loguru import logger

from config import SALARY_CAP, MAX_LINEUPS, MAX_PLAYER_EXPOSURE, MIN_PLAYER_EXPOSURE


class LineupOptimizer:
    """Advanced lineup optimization for NFL DFS"""
    
    def __init__(self, salary_cap: int = SALARY_CAP):
        self.salary_cap = salary_cap
        self.lineups = []
        self.player_exposure = {}
        
        # DraftKings NFL lineup requirements
        self.position_requirements = {
            'QB': 1,
            'RB': 2, 
            'WR': 3,
            'TE': 1,
            'FLEX': 1,  # RB/WR/TE
            'DST': 1,
            'K': 1
        }
        
        # Flex eligible positions
        self.flex_positions = ['RB', 'WR', 'TE']
        
    def prepare_player_pool(self, players_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare and validate player pool"""
        pool = players_df.copy()
        
        # Required columns
        required_cols = ['player_id', 'name', 'position', 'team', 'salary', 'projected_points']
        missing_cols = [col for col in required_cols if col not in pool.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Add default values for optional columns
        if 'projected_ownership' not in pool.columns:
            pool['projected_ownership'] = 10.0  # Default 10%
        if 'ceiling' not in pool.columns:
            pool['ceiling'] = pool['projected_points'] * 1.3
        if 'floor' not in pool.columns:
            pool['floor'] = pool['projected_points'] * 0.7
        if 'variance' not in pool.columns:
            pool['variance'] = (pool['ceiling'] - pool['floor']) / 4
        
        # Calculate value metrics
        pool['points_per_dollar'] = pool['projected_points'] / (pool['salary'] / 1000)
        pool['leverage'] = pool['projected_points'] / (pool['projected_ownership'] / 100)
        
        # Filter out invalid players
        pool = pool[
            (pool['salary'] > 0) & 
            (pool['projected_points'] > 0) &
            (pool['salary'] <= self.salary_cap)
        ].reset_index(drop=True)
        
        logger.info(f"Player pool prepared: {len(pool)} players")
        return pool
    
    def create_base_lineup(self, players_df: pd.DataFrame, strategy: str = 'balanced') -> Dict:
        """Create a single optimized lineup using linear programming"""
        
        # Create optimization problem
        prob = pulp.LpProblem("NFL_DFS_Lineup", pulp.LpMaximize)
        
        # Decision variables (binary: 1 if player selected, 0 otherwise)
        player_vars = {}
        for idx, player in players_df.iterrows():
            player_vars[idx] = pulp.LpVariable(f"player_{idx}", cat='Binary')
        
        # Objective function based on strategy
        if strategy == 'cash':
            # Cash games: maximize floor (safe points)
            objective = pulp.lpSum([
                player_vars[idx] * players_df.loc[idx, 'floor']
                for idx in players_df.index
            ])
        elif strategy == 'gpp':
            # GPP tournaments: maximize ceiling (upside)
            objective = pulp.lpSum([
                player_vars[idx] * players_df.loc[idx, 'ceiling']
                for idx in players_df.index
            ])
        else:
            # Balanced: maximize projected points
            objective = pulp.lpSum([
                player_vars[idx] * players_df.loc[idx, 'projected_points']
                for idx in players_df.index
            ])
        
        prob += objective
        
        # Salary cap constraint
        prob += pulp.lpSum([
            player_vars[idx] * players_df.loc[idx, 'salary']
            for idx in players_df.index
        ]) <= self.salary_cap
        
        # Position constraints
        for position, required_count in self.position_requirements.items():
            if position == 'FLEX':
                # FLEX can be RB, WR, or TE
                prob += pulp.lpSum([
                    player_vars[idx]
                    for idx in players_df.index
                    if players_df.loc[idx, 'position'] in self.flex_positions
                ]) >= required_count
            else:
                prob += pulp.lpSum([
                    player_vars[idx]
                    for idx in players_df.index
                    if players_df.loc[idx, 'position'] == position
                ]) == required_count
        
        # Total lineup size constraint (9 players for DraftKings NFL)
        prob += pulp.lpSum([player_vars[idx] for idx in players_df.index]) == 9
        
        # Solve the problem
        prob.solve(pulp.PULP_CBC_CMD(msg=0))
        
        if prob.status != pulp.LpStatusOptimal:
            logger.error(f"Optimization failed with status: {pulp.LpStatus[prob.status]}")
            return None
        
        # Extract selected players
        selected_players = []
        total_salary = 0
        total_points = 0
        
        for idx in players_df.index:
            if player_vars[idx].value() == 1:
                player = players_df.loc[idx].to_dict()
                selected_players.append(player)
                total_salary += player['salary']
                total_points += player['projected_points']
        
        lineup = {
            'players': selected_players,
            'total_salary': total_salary,
            'projected_points': total_points,
            'salary_remaining': self.salary_cap - total_salary,
            'strategy': strategy
        }
        
        return lineup

    def generate_multiple_lineups(self, players_df: pd.DataFrame, num_lineups: int = 20,
                                 strategies: List[str] = None) -> List[Dict]:
        """Generate multiple diverse lineups"""
        if strategies is None:
            strategies = ['balanced', 'cash', 'gpp']

        lineups = []
        used_combinations = set()

        # Prepare player pool
        pool = self.prepare_player_pool(players_df)

        for i in range(num_lineups):
            strategy = strategies[i % len(strategies)]

            # Add randomness to avoid identical lineups
            randomized_pool = pool.copy()
            randomized_pool['projected_points'] += np.random.normal(0, 0.5, len(pool))
            randomized_pool['ceiling'] += np.random.normal(0, 1.0, len(pool))
            randomized_pool['floor'] += np.random.normal(0, 0.3, len(pool))

            lineup = self.create_base_lineup(randomized_pool, strategy)

            if lineup:
                # Check for uniqueness
                player_ids = tuple(sorted([p['player_id'] for p in lineup['players']]))
                if player_ids not in used_combinations:
                    lineup['lineup_id'] = i + 1
                    lineups.append(lineup)
                    used_combinations.add(player_ids)

                    # Update exposure tracking
                    for player in lineup['players']:
                        pid = player['player_id']
                        self.player_exposure[pid] = self.player_exposure.get(pid, 0) + 1

        logger.info(f"Generated {len(lineups)} unique lineups")
        return lineups

    def apply_stacking_strategy(self, players_df: pd.DataFrame, stack_type: str = 'qb_wr') -> pd.DataFrame:
        """Apply stacking constraints to favor correlated players"""
        pool = players_df.copy()

        if stack_type == 'qb_wr':
            # Boost QB-WR combinations from same team
            for team in pool['team'].unique():
                team_qbs = pool[(pool['team'] == team) & (pool['position'] == 'QB')]
                team_wrs = pool[(pool['team'] == team) & (pool['position'] == 'WR')]

                if len(team_qbs) > 0 and len(team_wrs) > 0:
                    # Boost projections for stacking
                    qb_boost = 0.5
                    wr_boost = 0.3

                    pool.loc[team_qbs.index, 'projected_points'] += qb_boost
                    pool.loc[team_wrs.index, 'projected_points'] += wr_boost

        elif stack_type == 'game_stack':
            # Boost players from high-total games
            high_total_games = pool[pool.get('total_points', 0) > 47]['team'].unique()
            high_total_mask = pool['team'].isin(high_total_games)
            pool.loc[high_total_mask, 'projected_points'] *= 1.1

        return pool

    def optimize_with_constraints(self, players_df: pd.DataFrame, constraints: Dict) -> Dict:
        """Create lineup with custom constraints"""
        pool = self.prepare_player_pool(players_df)

        # Create optimization problem
        prob = pulp.LpProblem("NFL_DFS_Constrained", pulp.LpMaximize)

        # Decision variables
        player_vars = {}
        for idx, player in pool.iterrows():
            player_vars[idx] = pulp.LpVariable(f"player_{idx}", cat='Binary')

        # Objective function
        prob += pulp.lpSum([
            player_vars[idx] * pool.loc[idx, 'projected_points']
            for idx in pool.index
        ])

        # Standard constraints
        prob += pulp.lpSum([
            player_vars[idx] * pool.loc[idx, 'salary']
            for idx in pool.index
        ]) <= self.salary_cap

        # Position constraints
        for position, required_count in self.position_requirements.items():
            if position == 'FLEX':
                prob += pulp.lpSum([
                    player_vars[idx]
                    for idx in pool.index
                    if pool.loc[idx, 'position'] in self.flex_positions
                ]) >= required_count
            else:
                prob += pulp.lpSum([
                    player_vars[idx]
                    for idx in pool.index
                    if pool.loc[idx, 'position'] == position
                ]) == required_count

        prob += pulp.lpSum([player_vars[idx] for idx in pool.index]) == 9

        # Custom constraints
        if 'must_include' in constraints:
            for player_id in constraints['must_include']:
                player_indices = pool[pool['player_id'] == player_id].index
                if len(player_indices) > 0:
                    prob += player_vars[player_indices[0]] == 1

        if 'exclude' in constraints:
            for player_id in constraints['exclude']:
                player_indices = pool[pool['player_id'] == player_id].index
                if len(player_indices) > 0:
                    prob += player_vars[player_indices[0]] == 0

        if 'max_team_players' in constraints:
            max_team = constraints['max_team_players']
            for team in pool['team'].unique():
                team_indices = pool[pool['team'] == team].index
                prob += pulp.lpSum([player_vars[idx] for idx in team_indices]) <= max_team

        if 'min_salary' in constraints:
            prob += pulp.lpSum([
                player_vars[idx] * pool.loc[idx, 'salary']
                for idx in pool.index
            ]) >= constraints['min_salary']

        # Solve
        prob.solve(pulp.PULP_CBC_CMD(msg=0))

        if prob.status != pulp.LpStatusOptimal:
            return None

        # Extract lineup
        selected_players = []
        for idx in pool.index:
            if player_vars[idx].value() == 1:
                selected_players.append(pool.loc[idx].to_dict())

        return {
            'players': selected_players,
            'total_salary': sum(p['salary'] for p in selected_players),
            'projected_points': sum(p['projected_points'] for p in selected_players),
            'constraints_applied': constraints
        }

    def calculate_lineup_metrics(self, lineup: Dict) -> Dict:
        """Calculate advanced metrics for a lineup"""
        players = lineup['players']

        metrics = {
            'total_salary': sum(p['salary'] for p in players),
            'projected_points': sum(p['projected_points'] for p in players),
            'projected_ceiling': sum(p.get('ceiling', p['projected_points'] * 1.3) for p in players),
            'projected_floor': sum(p.get('floor', p['projected_points'] * 0.7) for p in players),
            'total_ownership': sum(p.get('projected_ownership', 10) for p in players),
            'avg_ownership': sum(p.get('projected_ownership', 10) for p in players) / len(players),
            'leverage_score': sum(p.get('leverage', 1) for p in players),
            'variance': sum(p.get('variance', 1) for p in players),
            'teams_used': len(set(p['team'] for p in players)),
            'salary_remaining': SALARY_CAP - sum(p['salary'] for p in players)
        }

        # Calculate position breakdown
        position_breakdown = {}
        for player in players:
            pos = player['position']
            if pos not in position_breakdown:
                position_breakdown[pos] = []
            position_breakdown[pos].append(player['name'])

        metrics['position_breakdown'] = position_breakdown

        # Calculate game stacks
        game_stacks = {}
        for player in players:
            team = player['team']
            game_stacks[team] = game_stacks.get(team, 0) + 1

        metrics['max_team_stack'] = max(game_stacks.values()) if game_stacks else 0
        metrics['team_distribution'] = game_stacks

        return metrics

    def export_lineups_csv(self, lineups: List[Dict], filename: str = None) -> str:
        """Export lineups to CSV format for DraftKings upload"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"lineups/nfl_lineups_{timestamp}.csv"

        # DraftKings CSV format
        csv_data = []

        for i, lineup in enumerate(lineups):
            players = lineup['players']

            # Sort players by position for consistent ordering
            position_order = ['QB', 'RB', 'RB', 'WR', 'WR', 'WR', 'TE', 'FLEX', 'DST', 'K']
            sorted_players = []

            # Group players by position
            pos_groups = {}
            for player in players:
                pos = player['position']
                if pos not in pos_groups:
                    pos_groups[pos] = []
                pos_groups[pos].append(player)

            # Fill positions in order
            lineup_positions = []
            for pos in ['QB', 'RB', 'WR', 'TE', 'DST', 'K']:
                if pos in pos_groups:
                    if pos == 'RB':
                        lineup_positions.extend(pos_groups[pos][:2])  # First 2 RBs
                    elif pos == 'WR':
                        lineup_positions.extend(pos_groups[pos][:3])  # First 3 WRs
                    else:
                        lineup_positions.extend(pos_groups[pos][:1])  # 1 of each other position

            # Add FLEX (remaining RB/WR/TE)
            flex_candidates = []
            if 'RB' in pos_groups and len(pos_groups['RB']) > 2:
                flex_candidates.extend(pos_groups['RB'][2:])
            if 'WR' in pos_groups and len(pos_groups['WR']) > 3:
                flex_candidates.extend(pos_groups['WR'][3:])
            if 'TE' in pos_groups and len(pos_groups['TE']) > 1:
                flex_candidates.extend(pos_groups['TE'][1:])

            if flex_candidates:
                lineup_positions.append(flex_candidates[0])

            # Create CSV row
            row = {
                'Lineup': i + 1,
                'QB': '',
                'RB1': '',
                'RB2': '',
                'WR1': '',
                'WR2': '',
                'WR3': '',
                'TE': '',
                'FLEX': '',
                'DST': '',
                'K': ''
            }

            # Map players to positions
            pos_counters = {'RB': 1, 'WR': 1}
            for player in lineup_positions:
                pos = player['position']
                name = f"{player['name']} ({player['team']})"

                if pos == 'QB':
                    row['QB'] = name
                elif pos == 'RB':
                    if pos_counters['RB'] <= 2:
                        row[f'RB{pos_counters["RB"]}'] = name
                        pos_counters['RB'] += 1
                    else:
                        row['FLEX'] = name
                elif pos == 'WR':
                    if pos_counters['WR'] <= 3:
                        row[f'WR{pos_counters["WR"]}'] = name
                        pos_counters['WR'] += 1
                    else:
                        row['FLEX'] = name
                elif pos == 'TE':
                    if row['TE'] == '':
                        row['TE'] = name
                    else:
                        row['FLEX'] = name
                elif pos == 'DST':
                    row['DST'] = name
                elif pos == 'K':
                    row['K'] = name

            csv_data.append(row)

        # Convert to DataFrame and save
        df = pd.DataFrame(csv_data)
        df.to_csv(filename, index=False)

        logger.info(f"Exported {len(lineups)} lineups to {filename}")
        return filename

    def get_exposure_report(self) -> pd.DataFrame:
        """Generate player exposure report"""
        if not self.player_exposure:
            return pd.DataFrame()

        exposure_data = []
        total_lineups = len(self.lineups)

        for player_id, count in self.player_exposure.items():
            exposure_pct = (count / total_lineups) * 100 if total_lineups > 0 else 0
            exposure_data.append({
                'player_id': player_id,
                'lineups_used': count,
                'exposure_pct': exposure_pct
            })

        df = pd.DataFrame(exposure_data)
        df = df.sort_values('exposure_pct', ascending=False)

        return df


# Example usage and testing
if __name__ == "__main__":
    # Create sample player data
    np.random.seed(42)

    sample_players = []
    positions = ['QB', 'RB', 'WR', 'TE', 'K', 'DST']
    teams = ['KC', 'BUF', 'SF', 'DAL', 'GB', 'NE', 'PIT', 'SEA']

    player_id = 1
    for team in teams:
        for pos in positions:
            count = 1 if pos in ['QB', 'K', 'DST'] else 3 if pos in ['RB', 'WR'] else 2

            for i in range(count):
                if pos == 'QB':
                    salary = np.random.randint(7000, 9000)
                    points = np.random.normal(18, 4)
                elif pos == 'RB':
                    salary = np.random.randint(4500, 8500)
                    points = np.random.normal(12, 5)
                elif pos == 'WR':
                    salary = np.random.randint(4000, 8000)
                    points = np.random.normal(11, 4)
                elif pos == 'TE':
                    salary = np.random.randint(3500, 7000)
                    points = np.random.normal(8, 3)
                elif pos == 'K':
                    salary = np.random.randint(4000, 5500)
                    points = np.random.normal(7, 2)
                else:  # DST
                    salary = np.random.randint(2500, 4000)
                    points = np.random.normal(6, 3)

                sample_players.append({
                    'player_id': f'player_{player_id}',
                    'name': f'{pos}{i+1}_{team}',
                    'position': pos,
                    'team': team,
                    'salary': int(salary),
                    'projected_points': max(0, points),
                    'projected_ownership': np.random.exponential(8),
                    'ceiling': max(0, points * 1.4),
                    'floor': max(0, points * 0.6)
                })
                player_id += 1

    players_df = pd.DataFrame(sample_players)

    # Test optimizer
    optimizer = LineupOptimizer()

    print("🏈 NFL DFS Lineup Optimizer Test")
    print("=" * 50)

    # Generate single lineup
    lineup = optimizer.create_base_lineup(players_df, strategy='balanced')
    if lineup:
        print(f"\n📋 Sample Lineup (${lineup['total_salary']}, {lineup['projected_points']:.1f} pts):")
        for player in lineup['players']:
            print(f"  {player['position']}: {player['name']} - ${player['salary']} ({player['projected_points']:.1f} pts)")

    # Generate multiple lineups
    lineups = optimizer.generate_multiple_lineups(players_df, num_lineups=5)
    print(f"\n📊 Generated {len(lineups)} lineups")

    for i, lineup in enumerate(lineups[:3]):
        metrics = optimizer.calculate_lineup_metrics(lineup)
        print(f"Lineup {i+1}: {metrics['projected_points']:.1f} pts, "
              f"${metrics['total_salary']}, {metrics['teams_used']} teams")

    # Test constraints
    constraints = {
        'must_include': ['player_1'],  # Force include QB1_KC
        'max_team_players': 3
    }

    constrained_lineup = optimizer.optimize_with_constraints(players_df, constraints)
    if constrained_lineup:
        print(f"\n🎯 Constrained Lineup: {constrained_lineup['projected_points']:.1f} pts")

    print("\n✅ Lineup optimizer test completed!")
